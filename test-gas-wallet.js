// Test script to verify gas wallet configuration
const { Web3 } = require('web3');
const axios = require('axios');

async function testGasWalletConfiguration() {
    console.log("=== Gas Wallet Configuration Test ===");
    
    // Your private key
    const gasPrivateKey = 'ad71990177f29e4df6ae6b88fe1c6c6aa078f48b7719d4a2b1744984d5aacebc';
    
    try {
        // Create Web3 instance
        const web3 = new Web3('https://bsc-dataseed.binance.org');
        
        // Ensure private key has correct format
        let cleanPrivateKey = gasPrivateKey;
        if (!cleanPrivateKey.startsWith('0x')) {
            cleanPrivateKey = '0x' + cleanPrivateKey;
        }
        
        console.log("1. Testing private key format...");
        console.log("Private key length:", gasPrivateKey.length);
        console.log("Private key (first 6 chars):", gasPrivateKey.substring(0, 6));
        
        // Derive wallet address from private key
        console.log("\n2. Deriving wallet address from private key...");
        const account = web3.eth.accounts.privateKeyToAccount(cleanPrivateKey);
        const derivedAddress = account.address;
        console.log("Derived wallet address:", derivedAddress);
        
        // Check BNB balance
        console.log("\n3. Checking BNB balance...");
        const provider = axios.create({
            baseURL: 'https://bsc-dataseed.binance.org'
        });
        
        const response = await provider.post('', {
            jsonrpc: '2.0',
            method: 'eth_getBalance',
            params: [derivedAddress, 'latest'],
            id: 1
        });
        
        const balanceWei = response.data.result;
        const balanceBNB = parseInt(balanceWei, 16) / (10 ** 18);
        
        console.log("BNB Balance (Wei):", balanceWei);
        console.log("BNB Balance (BNB):", balanceBNB);
        
        // Check if balance is sufficient for gas operations
        const minRequiredBNB = 0.01; // Minimum recommended balance
        console.log("\n4. Balance validation...");
        console.log("Minimum required BNB:", minRequiredBNB);
        console.log("Current balance:", balanceBNB);
        
        if (balanceBNB >= minRequiredBNB) {
            console.log("✅ Balance is sufficient for gas operations");
        } else {
            console.log("❌ Balance is insufficient for gas operations");
            console.log("Please add more BNB to this wallet:", derivedAddress);
        }
        
        // Test transaction count (nonce)
        console.log("\n5. Testing transaction count...");
        const nonceResponse = await provider.post('', {
            jsonrpc: '2.0',
            method: 'eth_getTransactionCount',
            params: [derivedAddress, 'latest'],
            id: 1
        });
        
        const nonce = parseInt(nonceResponse.data.result, 16);
        console.log("Current nonce:", nonce);
        
        console.log("\n=== Test Results ===");
        console.log("Gas Wallet Address:", derivedAddress);
        console.log("BNB Balance:", balanceBNB, "BNB");
        console.log("Transaction Count:", nonce);
        console.log("Configuration Status:", balanceBNB >= minRequiredBNB ? "✅ READY" : "❌ NEEDS MORE BNB");
        
        return {
            address: derivedAddress,
            balance: balanceBNB,
            nonce: nonce,
            ready: balanceBNB >= minRequiredBNB
        };
        
    } catch (error) {
        console.error("❌ Error testing gas wallet configuration:", error.message);
        console.error("Error stack:", error.stack);
        return null;
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testGasWalletConfiguration()
        .then(result => {
            if (result) {
                console.log("\n✅ Test completed successfully");
                process.exit(0);
            } else {
                console.log("\n❌ Test failed");
                process.exit(1);
            }
        })
        .catch(error => {
            console.error("❌ Test failed with error:", error.message);
            process.exit(1);
        });
}

module.exports = { testGasWalletConfiguration };
