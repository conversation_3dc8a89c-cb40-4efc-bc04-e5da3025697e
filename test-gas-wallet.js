module.exports = (sequelize, DataTypes) => {

    const Recharge = sequelize.define("recharge", {
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'tbl_users',
                key: 'id'
            }
        },
        transaction_id: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true
        },
        utr: {
            type: DataTypes.STRING,
            allowNull: true,
            defaultValue: ''
        },
        phone: {
            type: DataTypes.STRING,
            allowNull: false
        },
        money: {
            type: DataTypes.DOUBLE,
            allowNull: false,
            defaultValue: 0
        },
        type: {
            type: DataTypes.STRING,
            allowNull: false,
            defaultValue: 'MANUAL'
        },
        status: {
            type: DataTypes.INTEGER,
            allowNull: false,
            defaultValue: 0
        },
        time: {
            type: DataTypes.STRING,
            allowNull: false
        }
    }, {
        timestamps: false
    });

    return Recharge;

}
this is our rechargeModel 
const db = require('../../models');
const UserModel = db.user;
const RechargeModel = db.recharge;
this is where i am importing 
28|luckyrbm_socket  | Database error in transferUSDT: Cannot read properties of undefined (reading 'findAll')
28|luckyrbm_socket  | CRITICAL ERROR: Blockchain transaction successful but database update failed. Manual reconciliation required.
still getting error 