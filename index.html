<!DOCTYPE html>
<html lang="en">
<head>
<!-- Meta -->
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="title" content="Teshx - Your Partner in MLM Success" />
<meta name="description" content="Teshx empowers network marketers with innovative tools and strategies to grow their MLM business. Unlock potential, achieve goals, and create success with Teshx." />
<title>Teshx - Your Partner in MLM Success</title>
<link rel="shortcut icon" type="image/x-icon" href="img/Logo-Teshx-fav.png">
<link rel="stylesheet" href="assets/css/animate.css" >
<link rel="stylesheet" href="assets/bootstrap/css/bootstrap.min.css">
<link href="https://fonts.googleapis.com/css?family=Poppins:100,200,300,400,500,600,700,800,900" rel="stylesheet">
<link rel="stylesheet" href="assets/css/font-awesome.min.css" >
<link rel="stylesheet" href="assets/css/ionicons.min.css">
<link rel="stylesheet" href="assets/css/cryptocoins.css">
<link rel="stylesheet" href="assets/owlcarousel/css/owl.carousel.min.css">
<link rel="stylesheet" href="assets/owlcarousel/css/owl.theme.default.min.css">
<link rel="stylesheet" href="assets/css/magnific-popup.css">
<link rel="stylesheet" href="css/custom-styles.css">
<link rel="stylesheet" href="assets/css/spop.min.css">
<link rel="stylesheet" href="assets/css/style.css">
<link rel="stylesheet" href="assets/css/responsive.css">
<link id="layoutstyle" rel="stylesheet" href="assets/color/theme.css">

</head>

<body class="v_dark" data-spy="scroll" data-offset="110">

<!-- START LOADER -->
<!-- <div id="loader-wrapper">
    <div id="loading-center-absolute">
        <div class="object" id="object_four"></div>
        <div class="object" id="object_three"></div>
        <div class="object" id="object_two"></div>
        <div class="object" id="object_one"></div>
    </div>
    <div class="loader-section section-left"></div>
    <div class="loader-section section-right"></div>

</div> -->
<!-- END LOADER -->

<!-- START HEADER -->
 <style>

 </style>
<style>
  .title_default_light h4, .title_default_light p{
    color: rgb(26, 29, 95) !important;
  }
  .v_dark .box_wrap{
    border-radius: 20px;
    padding: 10px;
  }
  body{
    /* color: black !important; */
    background-color: white !important;
  }
  .bg_black_dark, .v_dark{
      /* color: black !important; */
      background-color: white !important;
  }
  /* Header Wrap Styles */
  .header_wrap {
    background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1);
    padding: 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1030;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .header_wrap::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, rgba(255,255,255,0), rgba(255,255,255,0.5), rgba(255,255,255,0));
    animation: shimmer 3s infinite;
  }

  @keyframes shimmer {
    0% { opacity: 0.3; }
    50% { opacity: 1; }
    100% { opacity: 0.3; }
  }

  .header_wrap::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGRlZnM+CiAgPHBhdHRlcm4gaWQ9InBhdHRlcm4iIHg9IjAiIHk9IjAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgcGF0dGVyblRyYW5zZm9ybT0icm90YXRlKDQ1KSI+CiAgICA8bGluZSB4MT0iMCIgeTE9IjAiIHgyPSIwIiB5Mj0iMjAiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIwLjUiIG9wYWNpdHk9IjAuMSIvPgogIDwvcGF0dGVybj4KPC9kZWZzPgo8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI3BhdHRlcm4pIiAvPgo8L3N2Zz4=');
    opacity: 0.1;
    z-index: -1;
  }

  header .navbar-nav a {
    color: #ffffff !important;
    font-weight: 600;
    letter-spacing: 0.5px;
    position: relative;
    padding: 10px 15px;
    transition: all 0.3s ease;
  }

  header .navbar-nav a:hover {
    color: #ffffff !important;
    transform: translateY(-2px);
  }

  .v_dark .nav-fixed, .v_dark header.active, .v_dark .navbar-nav .dropdown-menu, .v_dark .lng_dropdown .ddChild, .v_dark .spop, .v_light_dark .nav-fixed, .v_light_dark header.active, .v_light_dark .navbar-nav .dropdown-menu, .v_light_dark .lng_dropdown .ddChild, .v_light_dark .spop{
    background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1) !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  }

  /* Navbar Custom Styles */
  .navbar-custom {
    padding: 5px 0;
    transition: all 0.3s ease;
  }

  .navbar-custom .navbar-brand {
    padding: 0;
    margin-right: 2rem;
  }

  .logo-wrapper {
    position: relative;
    display: inline-block;
  }

  .logo-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.7) 0%, rgba(255, 255, 255, 0) 70%);
    filter: blur(5px);
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .navbar-brand:hover .logo-glow {
    opacity: 1;
    animation: pulse-glow 2s infinite;
  }

  @keyframes pulse-glow {
    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.7; }
    100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
  }

  .navbar-custom .navbar-nav {
    align-items: center;
  }

  .navbar-custom .nav-link {
    position: relative;
    overflow: hidden;
  }

  .nav-line {
    width: 0;
    height: 2px;
    background-color: #64c2cd;
    transition: all 0.3s ease;
    margin-top: 5px;
    border-radius: 2px;
  }

  .navbar-custom .nav-link:hover .nav-line,
  .navbar-custom .nav-link:focus .nav-line,
  .navbar-custom .nav-link.active .nav-line {
    width: 100%;
  }

  /* Login and Telegram buttons */
  .nav_btn .nav-item {
    margin-left: 5px;
  }

  .nav_btn .nav-link {
    background: linear-gradient(90deg, #3b9dc6, #8a5ad1);
    color: #ffffff !important;
    padding: 12px 20px !important;
    border-radius: 5px;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }

  .nav_btn .nav-link i {
    margin-right: 8px;
    font-size: 1rem;
  }

  .nav_btn .nav-link:hover {
    background: linear-gradient(90deg, #64c2cd, #3b9dc6);
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  }

  .nav_btn .nav-link:hover i {
    transform: translateX(3px);
  }

  .nav_btn .nav-line {
    background-color: #64c2cd;
  }

  .navbar-custom .btn-radius {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    margin-right: 15px;
  }

  .navbar-custom .btn-radius i {
    font-size: 1.5rem;
    color: #ffffff;
  }

  .navbar-custom .btn-radius:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
  }

  .navbar-custom .btn-default {
    background-color: #ffffff;
    color: #ff66cc !important;
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .navbar-custom .btn-default:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  /* Navbar Toggler Styles */
  .navbar-toggler {
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 15px;
    border-radius: 8px;
    color: #ffffff;
    transition: all 0.3s ease;
  }

  .navbar-toggler:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(255, 102, 204, 0.3);
  }

  .navbar-toggler:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }

  .navbar-toggler span {
    color: #ffffff;
    font-size: 1.5rem;
  }

  /* User Dropdown Styles */
  .user-menu {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: transparent;
    color: #ffffff !important;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    width: 100%;
    text-align: left;
  }

  .user-menu i {
    font-size: 1.2rem;
    margin-right: 10px;
  }

  .user-menu:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .user-dropdown {
    background: linear-gradient(135deg, #3b5998, #8a5ad1);
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 0;
    min-width: 200px;
    margin-top: 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    position: absolute;
    left: 0;
    right: 0;
    z-index: 1001;
    overflow: hidden;
  }

  .user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .dropdown-item {
    color: #ffffff !important;
    padding: 15px 20px;
    transition: all 0.3s ease;
    font-weight: 400;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .dropdown-item i {
    margin-right: 12px;
    font-size: 1rem;
  }

  .dropdown-item:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .dropdown-divider {
    border-color: rgba(255, 255, 255, 0.05);
    margin: 0;
  }

  /* Desktop Navbar Styles */
  @media (min-width: 992px) {
    .navbar-custom .navbar-brand {
      margin-right: 50px; /* Increase gap between logo and navbar */
    }

    .navbar-collapse {
      margin-left: 175px; /* Add additional space after logo */
    }
  }

  /* Single Dropdown Navbar Styles */
  .single-dropdown {
    width: 100%;
  }

  .single-dropdown .nav-item {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .single-dropdown .nav-item:last-child {
    border-bottom: none;
  }

  .single-dropdown .nav-link {
    color: #ffffff !important;
    font-weight: 500;
    letter-spacing: 1px;
    padding: 15px 20px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
  }

  .single-dropdown .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }

  .single-dropdown .nav-link:hover {
    background-color: rgba(100, 194, 205, 0.2);
  }

  /* Mobile Navbar Styles */
  @media (max-width: 991px) {
    .navbar-collapse {
      background: linear-gradient(135deg, #3b5998, #8a5ad1);
      padding: 0;
      margin-top: 15px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      z-index: 1000;
      display: none;
      overflow: hidden;
      transition: all 0.3s ease;
      transform: translateY(-10px);
      opacity: 0;
    }

    .navbar-collapse.show {
      display: block !important;
      transform: translateY(0);
      opacity: 1;
    }

    .navbar-nav {
      padding: 0;
      margin: 0;
    }

    /* Override any desktop styles for mobile */
    .navbar-custom {
      padding: 10px 0;
    }

    .navbar-toggler {
      border: none;
      padding: 5px;
      outline: none !important;
    }

    .navbar-toggler:focus {
      outline: none !important;
      box-shadow: none !important;
    }

    .navbar-toggler span {
      color: #ffffff;
      font-size: 1.5rem;
    }

    /* Clean single dropdown style */
    .single-dropdown {
      width: 100%;
      margin: 0;
      padding: 0;
    }

    .single-dropdown .nav-item {
      margin: 0;
      padding: 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .single-dropdown .nav-item:last-child {
      border-bottom: none;
    }

    .single-dropdown .nav-link {
      padding: 15px 20px !important;
      color: #ffffff !important;
      font-weight: 500;
      letter-spacing: 1px;
      display: flex;
      align-items: center;
      background: transparent !important;
      border-radius: 0 !important;
      box-shadow: none !important;
      border: none !important;
      justify-content: flex-start !important;
      transition: background-color 0.3s ease;
    }

    .single-dropdown .nav-link i {
      width: 20px;
      text-align: center;
      margin-right: 15px;
      font-size: 1rem;
    }

    .single-dropdown .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.05) !important;
      transform: none !important;
      box-shadow: none !important;
    }

    .single-dropdown .nav-link:hover i {
      transform: none !important;
    }

    /* Hide any nav-line elements */
    .nav-line {
      display: none !important;
    }
  }

  /* Enhanced Home Section Styles */
  #home_section {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    position: relative;
  }

  #home_section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('img/pattern.svg');
    opacity: 0.05;
    animation: backgroundMove 20s linear infinite;
  }

  @keyframes backgroundMove {
    0% { background-position: 0 0; }
    100% { background-position: 1000px 1000px; }
  }

  #home_section .banner_text_s2 h1 {
    font-size: 3.5rem;
    font-weight: 700;
    background: linear-gradient(to right, #1a1d5f, #4a4d8f);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 20px;
    transform: translateZ(0);
  }

  #home_section .banner_text_s2 h5 {
    font-size: 1.5rem;
    margin-bottom: 30px;
    color: #1a1d5f;
  }

  #home_section .btn-default,
  #home_section .btn-border {
    /* padding: 14px 35px; */
    border-radius: 50px;
    font-weight: 700;
    font-size: 16px;
    letter-spacing: 1.5px;
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    border: none;
    margin: 0 10px 10px 0;
    text-transform: uppercase;
  }

  #home_section .btn-default {
    background: linear-gradient(135deg, #3b5998, #8a5ad1);
    color: #fff;
  }

  #home_section .btn-border {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(5px);
  }

  #home_section .btn-default::before,
  #home_section .btn-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 0, 204, 0.5), rgba(51, 51, 255, 0.5));
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: -1;
    border-radius: 50px;
  }

  #home_section .btn-default:hover,
  #home_section .btn-border:hover {
    transform: translateY(-7px) scale(1.05);
    box-shadow: 0 15px 30px rgba(255, 0, 204, 0.2), 0 10px 20px rgba(51, 51, 255, 0.2);
    color: #fff;
  }

  #home_section .btn-default:hover::before {
    opacity: 0.5;
  }

  #home_section .btn-border:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
  }

  #home_section .btn-border:hover::before {
    opacity: 0.3;
  }

  #home_section .btn-default:active,
  #home_section .btn-border:active {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.1s ease;
  }

  #home_section .btn i.btn-icon {
    transition: all 0.3s ease;
    display: inline-block;
    margin-left: 8px;
    font-size: 14px;
    position: relative;
    top: 0;
  }

  #home_section .btn:hover i.btn-icon {
    transform: translateX(5px);
    color: rgba(255, 255, 255, 1);
  }

  #home_section .btn-default i.btn-icon {
    color: rgba(255, 255, 255, 0.9);
  }

  #home_section .btn-border i.btn-icon {
    color: rgba(255, 255, 255, 0.7);
  }

  #home_section .banner_image_right img {
    animation: float 6s ease-in-out infinite;
    max-width: 100%;
    filter: drop-shadow(0 20px 20px rgba(0,0,0,0.1));
  }

  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
  }

  .smart_cont {
    position: relative;
    overflow: hidden;
    z-index: 1;
    transition: all 0.3s ease;
  }

  .smart_cont:hover {
    transform: translateY(-5px);
  }

  .smart_cont::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255,255,255,0.1);
    transform: rotate(45deg);
    z-index: -1;
    transition: all 0.3s ease;
  }

  .smart_cont:hover::after {
    left: 100%;
  }

  /* Animated background shapes */
  .animated-bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
  }

  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(26, 29, 95, 0.05);
  }

  .shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    left: -150px;
    animation: shape-move-1 15s linear infinite;
  }

  .shape-2 {
    width: 200px;
    height: 200px;
    top: 50%;
    right: -100px;
    animation: shape-move-2 20s linear infinite;
  }

  .shape-3 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: 30%;
    animation: shape-move-3 25s linear infinite;
  }

  .shape-4 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation: shape-move-4 30s linear infinite;
  }

  @keyframes shape-move-1 {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(800px) rotate(360deg); }
  }

  @keyframes shape-move-2 {
    0% { transform: translateY(0) rotate(0deg); }
    100% { transform: translateY(-800px) rotate(-360deg); }
  }

  @keyframes shape-move-3 {
    0% { transform: translateX(0) rotate(0deg); }
    100% { transform: translateX(800px) rotate(360deg); }
  }

  @keyframes shape-move-4 {
    0% { transform: translateX(0) rotate(0deg); }
    100% { transform: translateX(-800px) rotate(-360deg); }
  }

  /* Simple text animations */
  .simple-fade {
    color: #fff;
    font-size: 4.2rem;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    margin-bottom: 30px;
    line-height: 1.2;
  }

  .highlight-text {
    position: relative;
    display: inline-block;
    color: transparent;
    background: linear-gradient(135deg,  #3b9dc6, #3b5998, #8a5ad1);
    -webkit-background-clip: text;
    background-clip: text;
    font-weight: 900;
    text-shadow: none;
    padding: 0 5px;
  }

  .highlight-text::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 5px;
    width: 100%;
    height: 8px;
    background: linear-gradient(45deg, rgba(255, 0, 204, 0.3), rgba(51, 51, 255, 0.3));
    z-index: -1;
    transform: scaleX(0);
    transform-origin: left;
    animation: highlightLine 1s ease-out forwards 0.5s;
  }

  @keyframes highlightLine {
    0% { transform: scaleX(0); }
    100% { transform: scaleX(1); }
  }

  .highlight {
    position: relative;
    color: #200044;
    font-weight: 600;
    padding: 0 5px;
  }

  .highlight::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40%;
    background: linear-gradient(90deg, rgba(255, 0, 204, 0.2), rgba(51, 51, 255, 0.2));
    z-index: -1;
    border-radius: 3px;
  }

  /* Image container and effects */
  .image-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
  }

  .main-image {
    position: relative;
    z-index: 2;
    animation: float 6s ease-in-out infinite;
    filter: drop-shadow(0 20px 20px rgba(0,0,0,0.1));
  }

  .glow-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(26, 29, 95, 0.3) 0%, rgba(26, 29, 95, 0) 70%);
    border-radius: 50%;
    z-index: 1;
    animation: pulse 4s ease-in-out infinite;
  }

  @keyframes pulse {
    0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
    100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.5; }
  }

  .particle-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
  }

  .particle {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(26, 29, 95, 0.6);
    box-shadow: 0 0 10px rgba(26, 29, 95, 0.8);
  }

  .p1 {
    top: 20%;
    left: 20%;
    animation: particle-move 8s linear infinite;
  }

  .p2 {
    top: 70%;
    left: 15%;
    animation: particle-move 12s linear infinite;
  }

  .p3 {
    top: 40%;
    left: 80%;
    animation: particle-move 10s linear infinite;
  }

  .p4 {
    top: 80%;
    left: 70%;
    animation: particle-move 14s linear infinite;
  }

  .p5 {
    top: 30%;
    left: 50%;
    animation: particle-move 9s linear infinite;
  }

  @keyframes particle-move {
    0% { transform: translate(0, 0); opacity: 0; }
    20% { opacity: 1; }
    80% { opacity: 1; }
    100% { transform: translate(20px, 20px); opacity: 0; }
  }

  /* Button enhancements */
  .btn-text {
    position: relative;
    z-index: 2;
  }

  /* Responsive adjustments */
  @media (max-width: 991px) {
    #home_section .banner_text_s2 h1 {
      font-size: 2.5rem;
    }

    #home_section .banner_text_s2 h5 {
      font-size: 1.2rem;
    }

    .shape-1 {
      width: 200px;
      height: 200px;
    }

    .shape-2 {
      width: 150px;
      height: 150px;
    }
  }

  @media (max-width: 767px) {
    #home_section .banner_text_s2 h1 {
      font-size: 2rem;
    }

    #home_section .banner_text_s2 h5 {
      font-size: 1rem;
    }

    #home_section .banner_image_right {
      margin-top: 30px;
    }

    .typing-animation {
      border-right: none;
      animation: none;
      white-space: normal;
    }

    .shape {
      display: none;
    }
  }
</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css">
<header class="header_wrap fixed-top">
	<div class="container-fluid">
		<nav class="navbar navbar-expand-lg navbar-custom">
			<div class="container" style="padding: 0 25px;">
				<a class="navbar-brand page-scroll animation" style="width: 80px; padding: 10px; position: relative;" href="index.html" data-animation="fadeInDown" data-animation-delay="1s">
					<div class="logo-wrapper">
						<img class="logo_light" src="img/Logo-Teshx-color.png" style="max-width: 108%; height: auto;" alt="logo" />
						<img class="logo_dark" src="img/Logo-Teshx-color.png" style="max-width: 108%; height: auto;" alt="logo" />
						<div class="logo-glow"></div>
					</div>
				</a>
				<button class="navbar-toggler animation" type="button" onclick="toggleNavbar(); return false;" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation" data-animation="fadeInDown" data-animation-delay="1.1s">
					<span class="ion-android-menu"></span>
				</button>
				<div class="collapse navbar-collapse" id="navbarSupportedContent">
						<ul class="navbar-nav single-dropdown">
							<li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="1.2s">
								<a class="nav-link page-scroll" href="index.html" onclick="if(window.innerWidth < 992) { document.getElementById('navbarSupportedContent').classList.remove('show'); }">
									<span>HOME</span>
								</a>
							</li>
							<li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="1.3s">
								<a class="nav-link page-scroll" href="#about" onclick="if(window.innerWidth < 992) { document.getElementById('navbarSupportedContent').classList.remove('show'); }">
									<span>ABOUT</span>
								</a>
							</li>
							<li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="1.4s">
								<a class="nav-link page-scroll" href="#business_plan" onclick="if(window.innerWidth < 992) { document.getElementById('navbarSupportedContent').classList.remove('show'); }">
									<span>BUSINESS PLAN</span>
								</a>
							</li>
							<!-- <li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="1.5s">
								<a class="nav-link page-scroll" target="_blank" onclick="if(window.innerWidth < 992) { document.getElementById('navbarSupportedContent').classList.remove('show'); }">
									<i class="fas fa-file-contract"></i> <span>CONTRACT</span>
								</a>
							</li> -->
							<li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="1.6s">
								<a class="nav-link page-scroll" href="#terms_conditions" onclick="if(window.innerWidth < 992) { document.getElementById('navbarSupportedContent').classList.remove('show'); }">
									<i class="fas fa-gavel"></i> <span>TERMS</span>
								</a>
							</li>
							<li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="1.9s">
								<a class="nav-link page-scroll">
									<i class="fas fa-sign-in-alt"></i> <span>LOGIN</span>
								</a>
							</li>
							<li class="nav-item animation" data-animation="fadeInDown" data-animation-delay="2s">
								<a class="nav-link page-scroll">
									<i class="fas fa-user-plus"></i> <span>REGISTER</span>
								</a>
							</li>
						</ul>
					</div>
				</div>
			</nav>
		</div>
	</header>
<!-- END HEADER -->

<!-- START SECTION BANNER -->
<section id="home_section" class="section_banner bg_black_dark" data-z-index="1" data-parallax="scroll" style="padding: 180px 0 0px; position: relative; overflow: hidden;">
    <div id="banner_bg_effect" class="banner_effect"></div>

    <!-- Animated background elements -->
    <div class="animated-bg-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
    </div>

    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 col-md-12 col-sm-12 order-lg-first">
                <div class="banner_text_s2 text_md_center">
                    <div class="col-md-5 smart_cont mr-auto text-center animation" style="margin-bottom: 20px; padding: 14px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50px; animation-delay: 2.3s; opacity: 1; box-shadow: 0 10px 30px rgba(51, 51, 255, 0.3), 0 5px 15px rgba(255, 0, 204, 0.2);" data-animation="fadeInUp" data-animation-delay="1.1s">
                      <span style="font-size:1rem; color: white; font-weight: 900; letter-spacing: 1px; text-shadow: 0 2px 5px rgba(0,0,0,0.2);">SMART•CONTRACT</span>
                    </div>

                    <h1 class="animation simple-fade" data-animation="fadeInUp" data-animation-delay="1.1s">
                        Welcome To <span class="highlight-text">TESHX</span>
                    </h1>

                    <h5 class="animation presale_txt" data-animation="fadeInUp" data-animation-delay="1.3s">
                        <span class="highlight" >Innovative Technology</span> Company
                    </h5>

                    <div class="btn_group pt-4 pb-4 animation btn-float-effect" data-animation="fadeInUp" data-animation-delay="1.4s">
                        <a href="https://teshx.com/soft/member/index.php" class="btn btn-default btn-radius nav-link content-popup">
                            <span class="btn-text">Login</span> <i class="fas fa-sign-in-alt btn-icon"></i>
                        </a>
                        <a href="https://teshx.com/soft/member/register.php" class="btn btn-border btn-radius">
                            <span class="btn-text">Join Now</span> <i class="fas fa-rocket btn-icon"></i>
                        </a>
                    </div>

                    <p class="animation" data-animation="fadeInUp" data-animation-delay="1.5s" style="max-width: 600px; margin: 0 auto 20px; color: rgb(24 26 83); font-size: 15px; line-height: 1.6;">
                      TESHX is an innovative technology company integrating satellite transmission, blockchain services, virtual roadmaps, and technology reserves to pioneer the infrastructure for future digital currencies and decentralized systems.
                    </p>
                    <!-- <span class=" icon_title animation" data-animation="fadeInUp" data-animation-delay="1.4s">We accept :</span>
                    <ul class="list_none currency_icon">
                        <li class="animation" data-animation="fadeInUp" data-animation-delay="1.5s"><i class="cc BTC-alt"></i><span>Bitcoin</span></li>
                        <li class="animation" data-animation="fadeInUp" data-animation-delay="1.6s"><i class="cc ETC"></i><span>Ethereum </span></li>
                        <li class="animation" data-animation="fadeInUp" data-animation-delay="1.7s"><i class="cc LTC-alt"></i><span>Litecoin</span></li>
                        <li class="animation" data-animation="fadeInUp" data-animation-delay="1.8s"><i class="cc XRP-alt"></i><span>Ripple</span></li>
                    </ul> -->
                    <div id="whitepaper" class="team_pop mfp-hide">
                        <div class="row m-0">
                            <div class="col-md-7">
                                <div class="pt-3 pb-3">
                                    <div class="title_dark title_border">
                                        <h4>Download Whitepaper</h4>
                                        <p>A purely peer-to-peer version of electronic cash would allow online payments to be sent directly from one party to another without going through a financial institution.Digital signatures provide part of the solution, but the main benefits are lost if a trusted third party is still required to prevent double-spending.</p>
                                        <p>The network timestamps transactions by hashing them into an ongoing chain of hash-based proof-of-work, forming a record that cannot be changed without redoing the proof-of-work.</p>
										<a href="index.html#" class="btn btn-default btn-radius">Download Now <i class="ion-ios-arrow-thin-right"></i></a>

                                    </div>
                                </div>
                            </div>
                            <div class="col-md-5">
                                <img class="pt-3 pb-3" src="assets/images/whitepaper.png" alt="whitepaper"/>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 col-md-12 col-sm-12 order-first">
                <div class="banner_image_right res_md_mb_50 res_xs_mb_30 animation" data-animation-delay="1.5s" data-animation="fadeInRight">
                    <div class="image-container" style="position: relative; display: flex; justify-content: center; align-items: center;">
                        <!-- Enhanced glow effect with dual layers -->
                        <div class="glow-effect-outer" style="position: absolute; width: 110%; height: 110%; background: radial-gradient(circle, rgba(138, 90, 209, 0.15) 0%, rgba(59, 89, 152, 0.1) 40%, transparent 70%); border-radius: 50%; filter: blur(25px); animation: pulseOuter 8s infinite alternate ease-in-out;"></div>
                        <div class="glow-effect" style="position: absolute; width: 100%; height: 100%; background: radial-gradient(circle, rgba(100, 194, 205, 0.2) 0%, rgba(59, 157, 198, 0.1) 40%, transparent 70%); border-radius: 50%; filter: blur(20px); animation: pulse 6s infinite alternate ease-in-out;"></div>

                        <!-- Main image with floating animation -->
                        <img alt="banner_vector2" src="assets/ethereum.png" class="main-image" style="position: relative; z-index: 2; max-width: 100%; transition: all 0.5s ease; animation: floatImage 5s infinite ease-in-out;">

                        <!-- Subtle rotating ring around the image -->
                        <div class="eth-ring" style="position: absolute; width: 105%; height: 105%; border: 2px solid rgba(100, 194, 205, 0.1); border-radius: 50%; z-index: 1; animation: rotate 20s linear infinite;"></div>
                        <div class="eth-ring-outer" style="position: absolute; width: 115%; height: 115%; border: 1px solid rgba(59, 157, 198, 0.1); border-radius: 50%; z-index: 1; animation: rotate 30s linear infinite reverse;"></div>

                        <!-- Enhanced particles with different sizes and colors -->
                        <div class="particle-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 1;">
                            <div class="particle p1" style="position: absolute; width: 8px; height: 8px; top: 20%; left: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); border-radius: 50%; opacity: 0.6; animation: float 6s infinite ease-in-out;"></div>
                            <div class="particle p2" style="position: absolute; width: 6px; height: 6px; bottom: 30%; right: 20%; background: linear-gradient(135deg, #3b9dc6, #3b5998); border-radius: 50%; opacity: 0.6; animation: float 8s infinite ease-in-out 1s;"></div>
                            <div class="particle p3" style="position: absolute; width: 10px; height: 10px; top: 70%; left: 25%; background: linear-gradient(135deg, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.6; animation: float 7s infinite ease-in-out 2s;"></div>
                            <div class="particle p4" style="position: absolute; width: 7px; height: 7px; top: 40%; right: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); border-radius: 50%; opacity: 0.5; animation: float 9s infinite ease-in-out 1.5s;"></div>
                            <div class="particle p5" style="position: absolute; width: 5px; height: 5px; bottom: 20%; left: 40%; background: linear-gradient(135deg, #8a5ad1, #3b5998); border-radius: 50%; opacity: 0.5; animation: float 7s infinite ease-in-out 3s;"></div>
                        </div>
                    </div>
                </div>

                <style>
                    @keyframes pulse {
                        0% { opacity: 0.5; transform: scale(0.95); }
                        100% { opacity: 0.8; transform: scale(1.05); }
                    }

                    @keyframes pulseOuter {
                        0% { opacity: 0.4; transform: scale(0.9); }
                        100% { opacity: 0.6; transform: scale(1.1); }
                    }

                    @keyframes float {
                        0% { transform: translateY(0) translateX(0); }
                        50% { transform: translateY(-10px) translateX(5px); }
                        100% { transform: translateY(0) translateX(0); }
                    }

                    @keyframes floatImage {
                        0% { transform: translateY(0); }
                        50% { transform: translateY(-8px); }
                        100% { transform: translateY(0); }
                    }

                    @keyframes rotate {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }

                    .image-container:hover .main-image {
                        transform: translateY(-5px) scale(1.05);
                        filter: drop-shadow(0 10px 15px rgba(100, 194, 205, 0.3));
                    }

                    .image-container:hover .glow-effect {
                        opacity: 0.9;
                        transform: scale(1.1);
                    }

                    .image-container:hover .eth-ring,
                    .image-container:hover .eth-ring-outer {
                        border-color: rgba(100, 194, 205, 0.2);
                    }

                    .image-container:hover .particle {
                        animation-duration: 4s;
                    }
                </style>
          	</div>
        </div>
    </div>
</section>
<!-- END SECTION BANNER -->

<!-- START SECTION SERVICES -->
<section id="about2" class="section-padding" style="background: linear-gradient(135deg, rgba(100, 194, 205, 0.1), rgba(59, 157, 198, 0.1)); padding: 60px 0;">
	<div class="container">
		<div class="row align-items-center">
			<div class="col-lg-8 offset-lg-2 col-md-12 col-sm-12">
				<div class="text-center mb-4 animation" data-animation="fadeInUp" data-animation-delay="0.2s">
                  <div style="color: #3b9dc6; font-size: 13px; font-weight: 500; letter-spacing: 1px; margin-bottom: 8px; text-transform: uppercase;">WHY CHOOSE US</div>
                  <h2 style="font-size: 24px; font-weight: 500; margin-bottom: 15px; color: #333; background: linear-gradient(90deg, #3b9dc6, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">Why Choose Teshx?</h2>
                  <p style="font-size: 14px; color: #555; max-width: 700px; margin: 0 auto; font-weight: 400; line-height: 1.6;">TESHX is at the forefront of integrating satellite transmission, blockchain services, and virtual roadmaps, offering innovative technology solutions and rewarding opportunities for our community members.</p>
                  <div style="width: 50px; height: 2px; background: linear-gradient(90deg, #3b9dc6, #8a5ad1); margin: 20px auto 0;"></div>
        		</div>
			</div>
        </div>
        <div class="row">
            <div class="col-lg-3 col-md-6 col-sm-12 mb-4">
            	<div class="animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 6px; padding: 20px 15px; height: 100%; box-shadow: 0 3px 10px rgba(0,0,0,0.04); transition: all 0.3s ease; border: 1px solid rgba(59, 157, 198, 0.1);">
                	<div style="width: 50px; height: 50px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center; background: rgba(59, 157, 198, 0.08); border-radius: 50%;">
                        <i class="fas fa-satellite" style="font-size: 18px; color: #3b9dc6;"></i>
                    </div>
                    <h4 style="font-size: 15px; font-weight: 500; margin-bottom: 8px; color: #333; text-align: center;">Satellite Transmission</h4>
                    <p style="color: #666; line-height: 1.5; font-size: 13px; text-align: center; font-weight: 400;">We develop decentralized satellite communication networks leveraging blockchain for secure, transparent data transmission.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12 mb-4">
            	<div onclick="read_more3(this);" class="animation" data-animation="fadeInUp" data-animation-delay="0.8s" style="background: white; border-radius: 6px; padding: 20px 15px; height: 100%; box-shadow: 0 3px 10px rgba(0,0,0,0.04); transition: all 0.3s ease; cursor: pointer; border: 1px solid rgba(138, 90, 209, 0.1);">
                    <div style="width: 50px; height: 50px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center; background: rgba(138, 90, 209, 0.08); border-radius: 50%;">
                        <i class="fas fa-project-diagram" style="font-size: 18px; color: #8a5ad1;"></i>
                    </div>
                    <h4 style="font-size: 15px; font-weight: 500; margin-bottom: 8px; color: #333; text-align: center;">Blockchain Services</h4>
                    <p style="color: #666; line-height: 1.5; font-size: 13px; text-align: center; font-weight: 400;">Our blockchain services provide secure platforms for DeFi, Smart Contracts, and Digital Identity Solutions
                      <span class="fc" style="color: #3b9dc6; font-weight: 500; cursor: pointer; font-size: 12px;">Read More</span>
                      <span class="sh" style="display: none;">that revolutionize the digital economy and empower communities globally.</span>
                    </p>
                </div>
            </div>
            <script>
                function read_more3(ele){
                      var e = $(ele).find("span.sh");
                      if (e.is(":visible")){
                          $(ele).find("span.fc").fadeIn();
                          $(ele).find("span.sh").fadeOut();
                      }
                      else{
                          $(ele).find("span.fc").fadeOut();
                          $(ele).find("span.sh").fadeIn();
                      }
                  }
            </script>
            <div class="col-lg-3 col-md-6 col-sm-12 mb-4">
            	<div class="animation" data-animation="fadeInUp" data-animation-delay="1s" style="background: white; border-radius: 6px; padding: 20px 15px; height: 100%; box-shadow: 0 3px 10px rgba(0,0,0,0.04); transition: all 0.3s ease; border: 1px solid rgba(59, 157, 198, 0.1);">
                    <div style="width: 50px; height: 50px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center; background: rgba(59, 157, 198, 0.08); border-radius: 50%;">
                        <i class="fas fa-map" style="font-size: 18px; color: #3b9dc6;"></i>
                    </div>
                    <h4 style="font-size: 15px; font-weight: 500; margin-bottom: 8px; color: #333; text-align: center;">Virtual Roadmaps</h4>
                    <p style="color: #666; line-height: 1.5; font-size: 13px; text-align: center; font-weight: 400;">Our team crafts dynamic virtual roadmaps to guide the evolution of emerging technologies and establish comprehensive technology reserves.</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 col-sm-12 mb-4">
            	<div class="animation" data-animation="fadeInUp" data-animation-delay="1s" style="background: white; border-radius: 8px; padding: 25px 20px; height: 100%; box-shadow: 0 5px 15px rgba(0,0,0,0.05); transition: all 0.3s ease; border: 1px solid rgba(0,0,0,0.03);">
                    <div style="width: 60px; height: 60px; margin: 0 auto 15px; display: flex; align-items: center; justify-content: center; background: rgba(102, 102, 255, 0.08); border-radius: 50%;">
                        <i class="fas fa-rocket" style="font-size: 22px; color: #6666ff;"></i>
                    </div>
                    <h4 style="font-size: 16px; font-weight: 500; margin-bottom: 10px; color: #0a1a2f; text-align: center;">Reward Plans</h4>
                    <p style="color: #777; line-height: 1.5; font-size: 14px; text-align: center; font-weight: 400;">Our comprehensive reward plans include Level ROI Income, Joining Amount ROI Income, Community Level Difference Income, and Team Rewards.</p>
                </div>
            </div>
    	</div>
  	</div>
</section>
<section id="about" class="small_pt" style="border-top: 1px solid #c3cfe2; position: relative; overflow: hidden; padding: 48px 0;">
  <!-- Enhanced Background with Gradient -->
  <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05), rgba(59, 89, 152, 0.05), rgba(138, 90, 209, 0.05)); z-index: 0;"></div>

  <!-- Animated Gradient Overlay -->
  <div style="position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: linear-gradient(45deg, rgba(100, 194, 205, 0.01), rgba(59, 157, 198, 0.01), rgba(59, 89, 152, 0.01), rgba(138, 90, 209, 0.01)); animation: rotate-gradient 30s linear infinite; z-index: 0;"></div>

  <!-- Animated Particles - More of them -->
  <div style="position: absolute; top: 20%; left: 10%; width: 8px; height: 8px; border-radius: 50%; background: rgba(100, 194, 205, 0.5); filter: blur(1px); animation: float-particle 15s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; top: 70%; left: 20%; width: 6px; height: 6px; border-radius: 50%; background: rgba(59, 157, 198, 0.5); filter: blur(1px); animation: float-particle 20s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; top: 30%; right: 15%; width: 10px; height: 10px; border-radius: 50%; background: rgba(138, 90, 209, 0.5); filter: blur(1px); animation: float-particle 18s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; bottom: 20%; right: 10%; width: 7px; height: 7px; border-radius: 50%; background: rgba(59, 89, 152, 0.5); filter: blur(1px); animation: float-particle 22s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; top: 50%; left: 30%; width: 5px; height: 5px; border-radius: 50%; background: rgba(100, 194, 205, 0.5); filter: blur(1px); animation: float-particle 25s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; bottom: 40%; right: 25%; width: 9px; height: 9px; border-radius: 50%; background: rgba(138, 90, 209, 0.5); filter: blur(1px); animation: float-particle 17s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; top: 15%; left: 40%; width: 4px; height: 4px; border-radius: 50%; background: rgba(59, 157, 198, 0.5); filter: blur(1px); animation: float-particle 19s infinite linear; z-index: 0;"></div>
  <div style="position: absolute; bottom: 60%; right: 40%; width: 6px; height: 6px; border-radius: 50%; background: rgba(59, 89, 152, 0.5); filter: blur(1px); animation: float-particle 21s infinite linear; z-index: 0;"></div>

  <!-- Network Lines Animation -->
  <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCw1MCBRMjUsMjUgNTAsNTAgVDEwMCw1MCIgc3Ryb2tlPSIjM2I5ZGM2IiBzdHJva2Utd2lkdGg9IjAuNSIgZmlsbD0ibm9uZSIgb3BhY2l0eT0iMC4wNSIvPjxwYXRoIGQ9Ik0wLDMwIFEzNSw2NSA3MCwzMCBUMTAwLDMwIiBzdHJva2U9IiM4YTVhZDEiIHN0cm9rZS13aWR0aD0iMC41IiBmaWxsPSJub25lIiBvcGFjaXR5PSIwLjA1Ii8+PHBhdGggZD0iTTAsNzAgUTQ1LDM1IDkwLDcwIFQxMDAsNzAiIHN0cm9rZT0iIzY0YzJjZCIgc3Ryb2tlLXdpZHRoPSIwLjUiIGZpbGw9Im5vbmUiIG9wYWNpdHk9IjAuMDUiLz48L3N2Zz4='); background-size: cover; z-index: 0; opacity: 0.7; animation: pulse-network 10s infinite ease-in-out;"></div>

  <div class="container" style="position: relative; z-index: 1;">
    <div class="row align-items-center">
      <!-- Image Column -->
      <div class="col-lg-6 col-md-12 col-sm-12">
        <div class="text_md_center">
          <div class="" data-animation="zoomIn" data-animation-delay="0.2s">
            <img class="img-fluid" src="assets/about-us.png" alt="aboutimg2"/>
            <div class="image-overlay"></div>
            <div class="eth-badge">
              <i class="fab fa-ethereum" style="margin-right: 5px; animation: bounce 2s infinite;"></i> Powered by Ethereum
            </div>
            <!-- Floating Elements Around Image -->
            <div style="position: absolute; top: 10%; left: 0; width: 15px; height: 15px; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.5; animation: float-around 8s infinite ease-in-out;"></div>
            <div style="position: absolute; top: 70%; right: 5%; width: 10px; height: 10px; border-radius: 50%; background: linear-gradient(135deg, #3b5998, #8a5ad1); opacity: 0.5; animation: float-around 12s infinite ease-in-out reverse;"></div>
            <div style="position: absolute; bottom: 20%; left: 10%; width: 12px; height: 12px; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.5; animation: float-around 10s infinite ease-in-out 2s;"></div>
          </div>
        </div>
      </div>

      <!-- Content Column -->
      <div class="col-lg-6 col-md-12 col-sm-12 res_md_mt_30 res_sm_mt_20">
        <div class="title_default_light title_border">
          <h4 class="about-title animation" data-animation="fadeInUp" data-animation-delay="0.2s">About Teshx</h4>
          <p class="animation" data-animation="fadeInUp" data-animation-delay="0.3s" style="margin-bottom: 15px; color: #555; line-height: 1.6;">
            TESHX is an innovative technology company at the forefront of integrating satellite transmission, blockchain services, virtual roadmaps, and technology reserves. Our mission is to pioneer the infrastructure that will underpin future digital currencies and decentralized systems.
          </p>

          <h5 class="animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="font-size: 16px; font-weight: 600; margin-bottom: 10px; color: #3b9dc6;">
            <i class="fas fa-globe" style="margin-right: 8px; color: #64c2cd;"></i> Satellite Transmission via Blockchain
          </h5>
          <p class="animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-bottom: 15px; color: #555; line-height: 1.6;">
            We are developing a decentralized satellite communication network that leverages blockchain technology to provide secure, transparent, and tamper-proof data transmission. This approach aims to enhance global connectivity, especially in underserved regions.
          </p>

          <h5 class="animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="font-size: 16px; font-weight: 600; margin-bottom: 10px; color: #3b9dc6;">
            <i class="fas fa-map" style="margin-right: 8px; color: #64c2cd;"></i> Virtual Roadmaps & Technology Reserves
          </h5>
          <p class="animation" data-animation="fadeInUp" data-animation-delay="0.7s" style="margin-bottom: 15px; color: #555; line-height: 1.6;">
            Our team is crafting dynamic virtual roadmaps to guide the evolution of emerging technologies. By establishing a comprehensive technology reserve, we aim to lay the groundwork for the development and implementation of future digital currencies and innovations.
          </p>

          <h5 class="animation" data-animation="fadeInUp" data-animation-delay="0.8s" style="font-size: 16px; font-weight: 600; margin-bottom: 10px; color: #3b9dc6;">
            <i class="fas fa-rocket" style="margin-right: 8px; color: #64c2cd;"></i> Projects in the Pipeline
          </h5>
          <p class="animation" data-animation="fadeInUp" data-animation-delay="0.9s" style="margin-bottom: 5px; color: #555; line-height: 1.6;">
            With numerous projects underway, we are committed to delivering solutions that will revolutionize the digital economy. Our focus areas include:
          </p>
          <ul class="feature-list" style="margin-bottom: 15px;">
            <li class="animation" data-animation="fadeInUp" data-animation-delay="1.0s">Decentralized Finance (DeFi): Creating platforms that facilitate secure and transparent financial transactions.</li>
            <li class="animation" data-animation="fadeInUp" data-animation-delay="1.1s">Smart Contracts: Developing self-executing contracts with the terms directly written into code.</li>
            <li class="animation" data-animation="fadeInUp" data-animation-delay="1.2s">Digital Identity Solutions: Establishing secure and verifiable digital identities for individuals and organizations.</li>
          </ul>

          <h5 class="animation" data-animation="fadeInUp" data-animation-delay="1.3s" style="font-size: 16px; font-weight: 600; margin-bottom: 10px; color: #3b9dc6;">
            <i class="fas fa-bullseye" style="margin-right: 8px; color: #64c2cd;"></i> Our Mission
          </h5>
          <p class="animation" data-animation="fadeInUp" data-animation-delay="1.4s" style="color: #555; line-height: 1.6;">
            To build a connected and inclusive world through cutting-edge technology, fostering innovation that transcends boundaries and empowers communities globally.
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
  @keyframes float-particle {
    0% {
      transform: translate(0, 0) rotate(0deg);
    }
    25% {
      transform: translate(50px, 25px) rotate(90deg);
    }
    50% {
      transform: translate(0, 50px) rotate(180deg);
    }
    75% {
      transform: translate(-50px, 25px) rotate(270deg);
    }
    100% {
      transform: translate(0, 0) rotate(360deg);
    }
  }

  @keyframes rotate-gradient {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes pulse-network {
    0%, 100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.05);
    }
  }

  @keyframes float-around {
    0%, 100% {
      transform: translate(0, 0);
    }
    25% {
      transform: translate(15px, -15px);
    }
    50% {
      transform: translate(0, -25px);
    }
    75% {
      transform: translate(-15px, -10px);
    }
  }

  @keyframes bounce {
    0%, 100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-3px);
    }
  }

  /* Image Animation */
  .about-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.5s ease;
    animation: pulse-shadow 5s infinite alternate;
  }

  @keyframes pulse-shadow {
    0% {
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }
    100% {
      box-shadow: 0 20px 40px rgba(59, 157, 198, 0.2), 0 10px 20px rgba(138, 90, 209, 0.2);
    }
  }

  .about-image-container:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(100, 194, 205, 0.2), rgba(59, 157, 198, 0.2), rgba(59, 89, 152, 0.2), rgba(138, 90, 209, 0.2));
    opacity: 0;
    transition: all 0.5s ease;
  }

  .about-image-container:hover .image-overlay {
    opacity: 1;
  }

  .eth-badge {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1);
    color: white;
    padding: 8px 20px;
    border-radius: 30px;
    font-size: 13px;
    font-weight: 500;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    white-space: nowrap;
    transition: all 0.3s ease;
    animation: badge-glow 3s infinite alternate;
  }

  @keyframes badge-glow {
    0% {
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    100% {
      box-shadow: 0 5px 20px rgba(100, 194, 205, 0.3), 0 5px 15px rgba(138, 90, 209, 0.3);
    }
  }

  .about-image-container:hover .eth-badge {
    transform: translateX(-50%) translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* List Styling */
  .feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .feature-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 12px;
    font-size: 15px;
    color: #555;
    line-height: 1.6;
    transition: all 0.3s ease;
  }

  .feature-list li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 5px;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1);
    border-radius: 50%;
    transition: all 0.3s ease;
    animation: pulse-bullet 2s infinite alternate;
  }

  @keyframes pulse-bullet {
    0% {
      transform: scale(1);
      box-shadow: 0 0 0 rgba(100, 194, 205, 0);
    }
    100% {
      transform: scale(1.1);
      box-shadow: 0 0 10px rgba(100, 194, 205, 0.3);
    }
  }

  .feature-list li:after {
    content: '✓';
    position: absolute;
    left: 5px;
    top: 5px;
    color: white;
    font-size: 10px;
    transition: all 0.3s ease;
  }

  .feature-list li:hover {
    transform: translateX(5px);
    color: #333;
  }

  .feature-list li:hover:before {
    transform: scale(1.2);
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }

  /* Title Styling */
  .about-title {
    position: relative;
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    display: inline-block;
  }

  .about-title:after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1);
    border-radius: 3px;
  }

  /* Responsive Fixes */
  @media (max-width: 767px) {
    .feature-list li {
      font-size: 14px;
      margin-bottom: 10px;
    }

    .about-title {
      font-size: 24px;
    }
  }
</style>
<!-- END SECTION SERVICES -->
<section id="howitworks" class="section-padding" style="padding: 70px 0; background: #fff; position: relative; overflow: hidden;">
  <!-- Background Elements -->
  <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05)); z-index: 0;"></div>

  <!-- Subtle Floating Elements -->
  <div style="position: absolute; top: -50px; left: -50px; width: 200px; height: 200px; border-radius: 50%; background: linear-gradient(135deg, rgba(59, 157, 198, 0.05), rgba(138, 90, 209, 0.05)); filter: blur(50px); z-index: 0; animation: float-blob 15s infinite ease-in-out;"></div>
  <div style="position: absolute; bottom: -80px; right: -80px; width: 300px; height: 300px; border-radius: 50%; background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05)); filter: blur(60px); z-index: 0; animation: float-blob 20s infinite ease-in-out reverse;"></div>

  <style>
    @keyframes float-blob {
      0%, 100% { transform: translate(0, 0) scale(1); }
      50% { transform: translate(30px, 20px) scale(1.1); }
    }

    .hiw-cards-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
      margin-bottom: 30px;
    }

    .hiw-card {
      background: white;
      border-radius: 16px;
      padding: 25px;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      border: 1px solid rgba(100, 194, 205, 0.1);
      position: relative;
      overflow: hidden;
      z-index: 1;
      flex: 1 1 300px;
      max-width: 380px;
      min-width: 280px;
    }

    .hiw-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 5px;
      height: 100%;
      background: linear-gradient(to bottom, #64c2cd, #3b9dc6, #8a5ad1);
      transition: all 0.4s ease;
    }

    .hiw-card:hover {
      transform: translateY(-15px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

    .hiw-card:hover::before {
      width: 100%;
      opacity: 0.05;
    }

    .hiw-icon-wrapper {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      background: linear-gradient(135deg, #64c2cd, #3b9dc6);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px;
      color: white;
      font-size: 24px;
      box-shadow: 0 8px 20px rgba(59, 157, 198, 0.2);
      transition: all 0.5s ease;
      position: relative;
      overflow: hidden;
    }

    .hiw-icon-wrapper::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
      transform: rotate(45deg);
      transition: all 0.5s ease;
    }

    .hiw-card:hover .hiw-icon-wrapper::before {
      animation: shine 1.5s ease;
    }

    @keyframes shine {
      0% { transform: translateX(-100%) rotate(45deg); }
      100% { transform: translateX(100%) rotate(45deg); }
    }

    .hiw-card:nth-child(even) .hiw-icon-wrapper {
      background: linear-gradient(135deg, #3b9dc6, #8a5ad1);
      box-shadow: 0 8px 20px rgba(138, 90, 209, 0.2);
    }

    .hiw-card:hover .hiw-icon-wrapper {
      transform: rotate(10deg) scale(1.1);
    }

    .hiw-card-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
      text-align: center;
      transition: all 0.3s ease;
      position: relative;
      display: inline-block;
      width: 100%;
    }

    .hiw-card-title::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 2px;
      background: linear-gradient(90deg, #64c2cd, #3b9dc6, #8a5ad1);
      transition: all 0.4s ease;
    }

    .hiw-card:hover .hiw-card-title::after {
      width: 50%;
    }

    .hiw-card-text {
      font-size: 14px;
      color: #666;
      line-height: 1.6;
      margin-bottom: 0;
      text-align: center;
      transition: all 0.3s ease;
    }

    .hiw-card:hover .hiw-card-text {
      color: #444;
    }

    .hiw-highlight {
      color: #fff;
      font-weight: 700;
      background: linear-gradient(90deg, #3b9dc6, #8a5ad1);
      padding: 4px 10px;
      border-radius: 20px;
      display: inline-block;
      margin: 8px 0;
      font-size: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }

    .hiw-card:hover .hiw-highlight {
      transform: scale(1.05);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    /* Responsive Adjustments */
    @media (max-width: 991px) {
      .hiw-card {
        flex: 1 1 250px;
        padding: 20px;
      }
    }

    @media (max-width: 767px) {
      .hiw-card {
        flex: 1 1 100%;
        max-width: 100%;
        padding: 20px;
      }

      .hiw-icon-wrapper {
        width: 50px;
        height: 50px;
        font-size: 20px;
      }

      .hiw-card-title {
        font-size: 17px;
      }

      .hiw-card-text {
        font-size: 13px;
      }
    }
  </style>

  <div class="container" style="position: relative; z-index: 1;">
    <div class="row align-items-center">
      <div class="col-lg-8 offset-lg-2 col-md-12 col-sm-12">
        <div class="title_default_light title_border text-center">
          <div style="color: #3b9dc6; font-size: 13px; font-weight: 500; letter-spacing: 1px; margin-bottom: 8px; text-transform: uppercase;" class="animation" data-animation="fadeInUp" data-animation-delay="0.1s">DISCOVER</div>
          <h4 style="font-size: 28px; font-weight: 600; margin-bottom: 15px; color: #333; background: linear-gradient(90deg, #3b9dc6, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" class="animation" data-animation="fadeInUp" data-animation-delay="0.2s">How It Works</h4>
          <div style="width: 60px; height: 3px; background: linear-gradient(90deg, #3b9dc6, #8a5ad1); margin: 0 auto 20px;"></div>
          <p style="font-size: 15px; color: #555; max-width: 700px; margin: 0 auto 40px; font-weight: 400; line-height: 1.6;" class="animation" data-animation="fadeInUp" data-animation-delay="0.3s">Everyone can have an honest income in an open and transparent system, keeping pace with advanced technology - The Fastest Way of Earning Ethereum</p>
        </div>
      </div>
    </div>

    <!-- First Row of Cards -->
    <div class="hiw-cards-container">
      <div class="hiw-card animation" data-animation="fadeInUp" data-animation-delay="0.4s">
        <div class="hiw-icon-wrapper">
          <i class="fas fa-shield-alt"></i>
        </div>
        <h5 class="hiw-card-title">Totally Risk Free</h5>
        <p class="hiw-card-text">Open-source Smart Contract based on Blockchain Ethereum. Powered by Ether Cryptocurrency - International and Decentralized.</p>
      </div>

      <div class="hiw-card animation" data-animation="fadeInUp" data-animation-delay="0.5s">
        <div class="hiw-icon-wrapper">
          <i class="fas fa-wallet"></i>
        </div>
        <h5 class="hiw-card-title">Pay-out from Smart Contract</h5>
        <p class="hiw-card-text">This concept is designed to transfer your money from wallet to wallet. All transactions are 100% from the contract address to participants' wallets.</p>
      </div>

      <div class="hiw-card animation" data-animation="fadeInUp" data-animation-delay="0.6s">
        <div class="hiw-icon-wrapper">
          <i class="fas fa-lock"></i>
        </div>
        <h5 class="hiw-card-title">Near Impossible to Hack</h5>
        <p class="hiw-card-text">It is designed in such a way that no one can change the algorithm or delete the members' rooms. The system is able to work without a site.</p>
      </div>
    </div>

    <!-- Second Row of Cards -->
    <div class="hiw-cards-container">
      <div class="hiw-card animation" data-animation="fadeInUp" data-animation-delay="0.7s">
        <div class="hiw-icon-wrapper">
          <i class="fas fa-users"></i>
        </div>
        <h5 class="hiw-card-title">For Everyone</h5>
        <p class="hiw-card-text">Become a Member in – 0.05 ether cryptocurrency. Open to anyone who wants to participate in this revolutionary platform.</p>
      </div>

      <div class="hiw-card animation" data-animation="fadeInUp" data-animation-delay="0.8s">
        <div class="hiw-icon-wrapper">
          <i class="fas fa-file-contract"></i>
        </div>
        <h5 class="hiw-card-title">Smart Contract is Easy & Simple</h5>
        <p class="hiw-card-text">It is easy to understand even for those who have not encountered such projects. <span class="hiw-highlight">IMPOSSIBLE TO SCAM</span></p>
      </div>

      <div class="hiw-card animation" data-animation="fadeInUp" data-animation-delay="0.9s">
        <div class="hiw-icon-wrapper">
          <i class="fas fa-link"></i>
        </div>
        <h5 class="hiw-card-title">Decentralized & Long Term</h5>
        <p class="hiw-card-text">This project runs on a decentralized Ethereum platform that will exist as long as the blockchain exists.</p>
      </div>
    </div>
  </div>
</section>

<!-- Vision and Mission Section -->
<section id="vision_mission" class="section-padding" style="background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05), rgba(59, 89, 152, 0.05), rgba(138, 90, 209, 0.05)); padding: 80px 0; position: relative; overflow: hidden; box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.05);">
    <!-- Section separator element -->
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, rgba(100, 194, 205, 0.2), rgba(59, 157, 198, 0.2), rgba(59, 89, 152, 0.2), rgba(138, 90, 209, 0.2)); z-index: 1;"></div>

    <!-- Animated background particles -->
    <div class="vision-bg-elements" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 0;">
        <div class="floating-shape shape-1" style="position: absolute; width: 150px; height: 150px; top: 10%; left: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 15s ease-in-out infinite;"></div>
        <div class="floating-shape shape-2" style="position: absolute; width: 100px; height: 100px; bottom: 10%; left: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 18s ease-in-out infinite 2s;"></div>
        <div class="floating-shape shape-3" style="position: absolute; width: 120px; height: 120px; top: 20%; right: 10%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 20s ease-in-out infinite 1s;"></div>
        <div class="floating-shape shape-4" style="position: absolute; width: 80px; height: 80px; bottom: 15%; right: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 17s ease-in-out infinite 3s;"></div>
    </div>

    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 offset-lg-2 col-md-12 col-sm-12">
                <div class="text-center mb-5 animation" data-animation="fadeInUp" data-animation-delay="0.2s">
                    <div class="section-tag" style="color: #3b9dc6; font-size: 14px; font-weight: 500; letter-spacing: 2px; margin-bottom: 10px; text-transform: uppercase;">OUR PHILOSOPHY</div>
                    <h2 class="section-title" style="font-size: 32px; font-weight: 600; margin-bottom: 15px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">Vision & Mission</h2>
                    <div class="title-separator" style="width: 80px; height: 3px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); margin: 0 auto 20px; border-radius: 3px;"></div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-lg-6 col-md-6 col-sm-12 mb-4">
                <div class="vision-card animation" data-animation="fadeInLeft" data-animation-delay="0.3s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden; height: 100%;">
                    <div class="card-label" style="position: absolute; top: 10px; right: 15px; font-size: 60px; font-weight: 800; opacity: 0.05; color: #3b9dc6;">VISION</div>

                    <div class="icon-wrapper" style="width: 70px; height: 70px; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); display: flex; align-items: center; justify-content: center; margin-bottom: 25px; position: relative; box-shadow: 0 10px 20px rgba(59, 157, 198, 0.2);">
                        <div class="icon-glow" style="position: absolute; width: 100%; height: 100%; border-radius: 50%; background: rgba(59, 157, 198, 0.5); filter: blur(10px); z-index: 0;"></div>
                        <i class="ion-ios-eye-outline" style="font-size: 36px; color: white; position: relative; z-index: 1;"></i>
                    </div>

                    <h3 style="font-size: 22px; font-weight: 600; margin-bottom: 15px; color: #333;">Our Vision</h3>

                    <p style="font-size: 15px; color: #666; line-height: 1.6; margin-bottom: 15px;">
                        To revolutionize the digital economy through cutting-edge blockchain technology, creating a world where financial prosperity is accessible to all. TESHX aims to be the bridge connecting traditional finance with the future of decentralized systems, empowering individuals to take control of their financial destiny regardless of their background or location.
                    </p>

                    <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
                </div>
            </div>

            <div class="col-lg-6 col-md-6 col-sm-12 mb-4">
                <div class="mission-card animation" data-animation="fadeInRight" data-animation-delay="0.3s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden; height: 100%;">
                    <div class="card-label" style="position: absolute; top: 10px; right: 15px; font-size: 60px; font-weight: 800; opacity: 0.05; color: #8a5ad1;">MISSION</div>

                    <div class="icon-wrapper" style="width: 70px; height: 70px; border-radius: 50%; background: linear-gradient(135deg, #3b5998, #8a5ad1); display: flex; align-items: center; justify-content: center; margin-bottom: 25px; position: relative; box-shadow: 0 10px 20px rgba(138, 90, 209, 0.2);">
                        <div class="icon-glow" style="position: absolute; width: 100%; height: 100%; border-radius: 50%; background: rgba(138, 90, 209, 0.5); filter: blur(10px); z-index: 0;"></div>
                        <i class="ion-ios-flag-outline" style="font-size: 36px; color: white; position: relative; z-index: 1;"></i>
                    </div>

                    <h3 style="font-size: 22px; font-weight: 600; margin-bottom: 15px; color: #333;">Our Mission</h3>

                    <p style="font-size: 15px; color: #666; line-height: 1.6; margin-bottom: 15px;">
                        To build a thriving ecosystem that rewards participation, fosters community growth, and creates sustainable wealth opportunities through blockchain innovation. We are committed to providing secure, transparent, and user-friendly platforms that deliver exceptional value to our community members while maintaining the highest standards of integrity, reliability, and customer service excellence.
                    </p>

                    <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b5998, #8a5ad1); transition: all 0.5s ease;"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
  function read_more(ele){
      var e = $(ele).find("span");
      if (e.is(":visible")){
          $(ele).find("p.fc").fadeIn();
          $(ele).find("span").fadeOut();
      }
      else{
          $(ele).find("p.fc").fadeOut();
          $(ele).find("span").fadeIn();
      }
  }
  function read_more2(ele){
      var e = $(ele).find("span");
      if (e.is(":visible")){
          $(ele).find("i").fadeIn();
          $(ele).find("span").fadeOut();
      }
      else{
          $(ele).find("i").fadeOut();
          $(ele).find("span").fadeIn();
      }
  }
</script>

<!-- START ABOUT US SECTION -->
<section id="about2" class="section-padding" style="background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05), rgba(59, 89, 152, 0.05), rgba(138, 90, 209, 0.05)); padding: 100px 0; position: relative; overflow: hidden; box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.05);">
    <!-- Section separator element -->
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, rgba(100, 194, 205, 0.2), rgba(59, 157, 198, 0.2), rgba(59, 89, 152, 0.2), rgba(138, 90, 209, 0.2)); z-index: 1;"></div>
    <!-- Animated background particles -->
    <div class="about-bg-elements" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 0;">
        <div class="floating-shape shape-1" style="position: absolute; width: 150px; height: 150px; top: 10%; left: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 15s ease-in-out infinite;"></div>
        <div class="floating-shape shape-2" style="position: absolute; width: 100px; height: 100px; bottom: 10%; left: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 18s ease-in-out infinite 2s;"></div>
        <div class="floating-shape shape-3" style="position: absolute; width: 120px; height: 120px; top: 20%; right: 10%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 20s ease-in-out infinite 1s;"></div>
        <div class="floating-shape shape-4" style="position: absolute; width: 80px; height: 80px; bottom: 15%; right: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 17s ease-in-out infinite 3s;"></div>
    </div>
  <div class="container">
      <div class="row align-items-center">
          <div class="col-lg-6 col-md-12 col-sm-12">
            <div class="about-image-wrapper animation" data-animation="zoomIn" data-animation-delay="0.2s" style="position: relative; padding: 20px;">
                    <!-- Glowing background effect -->
                    <div class="image-glow" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80%; height: 80%; background: linear-gradient(135deg, rgba(100, 194, 205, 0.2), rgba(59, 157, 198, 0.2), rgba(59, 89, 152, 0.2), rgba(138, 90, 209, 0.2)); filter: blur(40px); border-radius: 50%; z-index: 0;"></div>

                    <!-- Image container with animation -->
                    <div class="image-container" style="position: relative; z-index: 1; transform-style: preserve-3d; perspective: 1000px;">
                        <img class="about-image" src="img/logo.svg" alt="aboutimg2" style="max-width: 100%; transform: translateZ(20px); transition: all 0.5s ease;"/>

                        <!-- Floating particles around the image -->
                        <div class="image-particles" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                            <span class="particle p1" style="position: absolute; width: 8px; height: 8px; top: 20%; left: 10%; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.6; animation: particleFloat 6s ease-in-out infinite;"></span>
                            <span class="particle p2" style="position: absolute; width: 8px; height: 8px; top: 70%; left: 15%; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.6; animation: particleFloat 8s ease-in-out infinite 1s;"></span>
                            <span class="particle p3" style="position: absolute; width: 8px; height: 8px; top: 30%; right: 15%; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.6; animation: particleFloat 7s ease-in-out infinite 2s;"></span>
                            <span class="particle p4" style="position: absolute; width: 8px; height: 8px; top: 80%; right: 10%; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.6; animation: particleFloat 9s ease-in-out infinite 3s;"></span>
                            <span class="particle p5" style="position: absolute; width: 8px; height: 8px; top: 50%; left: 50%; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); opacity: 0.6; animation: particleFloat 10s ease-in-out infinite 4s;"></span>
                        </div>
                    </div>
                </div>
          </div>
          <div class="col-lg-6 col-md-12 col-sm-12 res_md_mt_30 res_sm_mt_20">
              <div class="about-content">
                    <!-- Section tag -->
                    <!-- <div class="section-tag animation" data-animation="fadeInUp" data-animation-delay="0.1s" style="color: #3b9dc6; font-size: 14px; font-weight: 500; letter-spacing: 2px; margin-bottom: 10px; text-transform: uppercase;">ABOUT US</div> -->

                    <!-- Section title with gradient -->
                    <h2 class="about-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="position: relative; font-size: 32px; font-weight: 600; margin-bottom: 25px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; display: inline-block;">
                        Why Choose Us?
                    </h2>

                    <!-- Title separator -->
                    <!-- <div class="title-separator animation" data-animation="fadeInUp" data-animation-delay="0.3s" style="width: 80px; height: 3px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); margin-bottom: 25px; border-radius: 3px;"></div> -->
                <!-- <h6 style="color: white" class="animation" data-animation="fadeInUp" data-animation-delay="0.3s">Here’s why millions of users Deposit with us</h6> -->
                <!-- <h5 style="color: white;" class="animation" data-animation="fadeInUp" data-animation-delay="0.4s">Deep Expertise and Focus on Quality</h5> -->
                    <!-- Feature list with icons -->
                    <div class="feature-list">
                        <div class="feature-item animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="display: flex; margin-bottom: 25px; align-items: flex-start; transition: all 0.3s ease;">
                            <div class="feature-icon" style="min-width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(59, 157, 198, 0.2); transition: all 0.3s ease;">
                                <i class="ion-ios-infinite-outline" style="font-size: 24px; color: white;"></i>
                            </div>
                            <div class="feature-text">
                                <h4 style="font-size: 18px; font-weight: 600; margin-bottom: 10px; color: #333;">Endless Opportunities</h4>
                                <p style="font-size: 15px; color: #666; line-height: 1.6;">When you join Teshx, you open the doors to endless access to one of its kind smart contract. Which enables beginner & experienced people to earn more with a very small capital.</p>
                            </div>
                        </div>

                        <div class="feature-item animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="display: flex; margin-bottom: 25px; align-items: flex-start; transition: all 0.3s ease;">
                            <div class="feature-icon" style="min-width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #3b9dc6, #3b5998); display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(59, 89, 152, 0.2); transition: all 0.3s ease;">
                                <i class="ion-ios-people-outline" style="font-size: 24px; color: white;"></i>
                            </div>
                            <div class="feature-text">
                                <h4 style="font-size: 18px; font-weight: 600; margin-bottom: 10px; color: #333;">Smart Community</h4>
                                <p style="font-size: 15px; color: #666; line-height: 1.6;">Eventually building a smart community that thrives on cutting edge technology. Hance making Teshx as leader in its class.</p>
                            </div>
                        </div>

                        <div class="feature-item animation" data-animation="fadeInUp" data-animation-delay="0.8s" style="display: flex; margin-bottom: 25px; align-items: flex-start; transition: all 0.3s ease;">
                            <div class="feature-icon" style="min-width: 50px; height: 50px; border-radius: 50%; background: linear-gradient(135deg, #3b5998, #8a5ad1); display: flex; align-items: center; justify-content: center; margin-right: 20px; box-shadow: 0 10px 20px rgba(138, 90, 209, 0.2); transition: all 0.3s ease;">
                                <i class="ion-ios-pulse" style="font-size: 24px; color: white;"></i>
                            </div>
                            <div class="feature-text">
                                <h4 style="font-size: 18px; font-weight: 600; margin-bottom: 10px; color: #333;">Innovative Platform</h4>
                                <p style="font-size: 15px; color: #666; line-height: 1.6;">The only platform to introduce compounding based smart contract. Which is built on ethereum platform, benefiting all trades of people in crypto world.</p>
                            </div>
                        </div>
                    </div>


              </div>
              <!-- <a href="https://www.youtube.com/watch?v=ZE2HxTmxfrI" class="btn btn-default btn-radius video animation" data-animation="fadeInUp" data-animation-delay="1s">Let's Start <i class="ion-ios-arrow-thin-right"></i></a>  -->
          </div>
      </div>
  </div>

    <style>
        @keyframes floatAnimation {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(15px, 15px) rotate(5deg); }
            50% { transform: translate(5px, -10px) rotate(10deg); }
            75% { transform: translate(-10px, 5px) rotate(5deg); }
            100% { transform: translate(0, 0) rotate(0deg); }
        }

        @keyframes particleFloat {
            0% { transform: translate(0, 0); opacity: 0.2; }
            50% { transform: translate(10px, 10px); opacity: 0.8; }
            100% { transform: translate(0, 0); opacity: 0.2; }
        }

        #about2 .feature-item:hover {
            transform: translateX(5px);
        }

        #about2 .feature-item:hover .feature-icon {
            transform: rotate(10deg) scale(1.1);
        }

        #about2 .image-container:hover .about-image {
            transform: translateZ(30px) scale(1.05);
        }

        #about2 .cta-button a:hover {
            box-shadow: 0 10px 20px rgba(59, 157, 198, 0.3);
        }

        #about2 .cta-button a:hover .btn-icon {
            transform: translateX(5px);
        }

        @media (max-width: 991px) {
            #about2 {
                padding: 80px 0;
            }

            #about2 .about-title {
                font-size: 28px;
            }

            #about2 .feature-item {
                margin-bottom: 20px;
            }
        }

        @media (max-width: 767px) {
            #about2 {
                padding: 60px 0;
            }

            #about2 .about-title {
                font-size: 24px;
            }

            #about2 .feature-icon {
                min-width: 40px;
                height: 40px;
            }

            #about2 .feature-icon i {
                font-size: 20px;
            }

            #about2 .feature-text h4 {
                font-size: 16px;
            }

            #about2 .feature-text p {
                font-size: 14px;
            }
        }
    </style>
</section>

<!-- <section  class="small_pt">
  <div class="container">
      <div class="row align-items-center">
          <div class="col-lg-12 col-md-12 col-sm-12 res_md_mt_30 res_sm_mt_20">
              <div class="title_default_light title_border text_md_center mx-auto text-center">
                <h3 style="font-weight: 700;" class="animation" data-animation="fadeInUp" data-animation-delay="0.2s">Dividend Illustration : 100ETH</h3><br><Br>
                <div class="table-responsive">
                  <table class="table">
                    <tr>
                      <td style="font-weight: 700;">Days</td>
                      <td style="font-weight: 700;">Contract ETH</td>
                      <td style="font-weight: 700;">Dividends</td>
                      <td style="font-weight: 700;">40% payout</td>
                      <td style="font-weight: 700;">60% to compound</td>
                      <td style="font-weight: 700;">Withdrawble wallet</td>
                    </tr>
                    <tr>
                      <td>1</td>
                      <td>100</td>
                      <td>1.5</td>
                      <td>0.6</td>
                      <td>0.9</td>
                      <td>0.6</td>
                    </tr>
                    <tr>
                      <td>2</td>
                      <td>100.9</td>
                      <td>1.514</td>
                      <td>0.605</td>
                      <td>0.908</td>
                      <td>1.205</td>
                    </tr>
                    <tr>
                      <td>3</td>
                      <td>101.808</td>
                      <td>1.527</td>
                      <td>0.611</td>
                      <td>0.916</td>
                      <td>1.816</td>
                    </tr>
                    <tr>
                      <td>25</td>
                      <td>123.99</td>
                      <td>1.86</td>
                      <td>0.744</td>
                      <td>1.116</td>
                      <td>16.738</td>
                    </tr>
                    <tr>
                      <td>50</td>
                      <td>155.12</td>
                      <td>2.327</td>
                      <td>0.931</td>
                      <td>1.396</td>
                      <td>37.677</td>
                    </tr>
                    <tr>
                      <td>100</td>
                      <td>242.787</td>
                      <td>3.642</td>
                      <td>1.457</td>
                      <td>2.185</td>
                      <td>96.648</td>
                    </tr>
                    <tr>
                      <td>101</td>
                      <td>244.972</td>
                      <td>3.675</td>
                      <td>1.47</td>
                      <td>2.205</td>
                      <td>98.118</td>
                    </tr>
                    <tr>
                      <td>102</td>
                      <td>247.177</td>
                      <td>3.708</td>
                      <td>1.483</td>
                      <td>2.225</td>
                      <td>99.601</td>
                    </tr>
                    <tr>
                      <td>103</td>
                      <td>249.401</td>
                      <td>3.741</td>
                      <td>1.496</td>
                      <td>2.245</td>
                      <td>101.097</td>
                    </tr>
                    <tr>
                      <td>125</td>
                      <td>303.742</td>
                      <td>4.556</td>
                      <td>1.822</td>
                      <td>2.734</td>
                      <td>137.65</td>
                    </tr>
                    <tr>
                      <td>150</td>
                      <td>380</td>
                      <td>5.7</td>
                      <td>2.28</td>
                      <td>3.42</td>
                      <td>188.947</td>
                    </tr>
                    <tr>
                      <td>200</td>
                      <td>594.76</td>
                      <td>8.921</td>
                      <td>3.569</td>
                      <td>5.335</td>
                      <td>333.409</td>
                    </tr>
                    <tr>
                      <td>201</td>
                      <td>600.113</td>
                      <td>9.002</td>
                      <td>3.601</td>
                      <td>5.401</td>
                      <td>337.01</td>
                    </tr>
                    <tr>
                      <td>202</td>
                      <td>605.514</td>
                      <td>9.083</td>
                      <td>3.633</td>
                      <td>5.45</td>
                      <td>340.643</td>
                    </tr>
                  </table>
                </div>
              </div>
          </div>
      </div>
  </div>
</section> -->
<!-- <section  class="small_pt">
  <div class="container">
      <div class="row align-items-center">
          <div class="col-lg-6 col-md-12 col-sm-12">
            <div class="text_md_center">
                <img class="animation" data-animation="zoomIn" data-animation-delay="0.2s" src="assets/why_choose_us.png" alt="aboutimg2"/>
              </div>
          </div>
          <div class="col-lg-6 col-md-12 col-sm-12 res_md_mt_30 res_sm_mt_20">
              <div class="title_default_light title_border">
                <h3 style="font-weight: 700;" class="animation" data-animation="fadeInUp" data-animation-delay="0.2s">Total receivable <span style="color: rgb(26, 29, 95) !important;">340%</span> in 4 Ways</h3><br>
                <ul>
                    <li class="animation" data-animation="fadeInUp" data-animation-delay="0.8s">1.5% Daily Dividend Income for 202 days
                      (Compounding ratio 60:40</li>
                    <li class="animation" data-animation="fadeInUp" data-animation-delay="0.8s">20% Dividend Compensation Plan</li>
                    <li class="animation" data-animation="fadeInUp" data-animation-delay="0.8s">130% Dividend referral bonus</li>
                    <li class="animation" data-animation="fadeInUp" data-animation-delay="0.8s">2% Daily Top Sponsors Pool</li>
                </ul>
              </div>
          </div>
      </div>
  </div>
</section> -->

<!-- START BUSINESS PLAN SECTION -->
<section id="business_plan" class="section-padding" style="background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05), rgba(59, 89, 152, 0.05), rgba(138, 90, 209, 0.05)); padding: 100px 0; position: relative; overflow: hidden; box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.05);">
    <!-- Section separator element -->
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, rgba(100, 194, 205, 0.2), rgba(59, 157, 198, 0.2), rgba(59, 89, 152, 0.2), rgba(138, 90, 209, 0.2)); z-index: 1;"></div>

    <!-- Animated background particles -->
    <div class="business-bg-elements" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 0;">
        <!-- Large floating shapes -->
        <div class="floating-shape shape-1" style="position: absolute; width: 150px; height: 150px; top: 10%; left: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 15s ease-in-out infinite;"></div>
        <div class="floating-shape shape-2" style="position: absolute; width: 100px; height: 100px; bottom: 10%; left: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 18s ease-in-out infinite 2s;"></div>
        <div class="floating-shape shape-3" style="position: absolute; width: 120px; height: 120px; top: 20%; right: 10%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 20s ease-in-out infinite 1s;"></div>
        <div class="floating-shape shape-4" style="position: absolute; width: 80px; height: 80px; bottom: 15%; right: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 17s ease-in-out infinite 3s;"></div>

        <!-- Medium floating shapes -->
        <div class="floating-shape shape-5" style="position: absolute; width: 60px; height: 60px; top: 35%; left: 25%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); border-radius: 50%; opacity: 0.08; filter: blur(15px); animation: floatAnimation 12s ease-in-out infinite 1.5s;"></div>
        <div class="floating-shape shape-6" style="position: absolute; width: 50px; height: 50px; bottom: 30%; right: 25%; background: linear-gradient(135deg, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.08; filter: blur(15px); animation: floatAnimation 14s ease-in-out infinite 0.5s;"></div>
        <div class="floating-shape shape-7" style="position: absolute; width: 70px; height: 70px; top: 60%; left: 40%; background: linear-gradient(135deg, #3b9dc6, #3b5998); border-radius: 50%; opacity: 0.08; filter: blur(15px); animation: floatAnimation 16s ease-in-out infinite 2.5s;"></div>

        <!-- Small particles -->
        <div class="particle-group" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
            <div class="particle" style="position: absolute; width: 6px; height: 6px; top: 15%; left: 30%; background: #64c2cd; border-radius: 50%; opacity: 0.4; animation: particleFloat 8s ease-in-out infinite;"></div>
            <div class="particle" style="position: absolute; width: 8px; height: 8px; top: 25%; right: 35%; background: #3b9dc6; border-radius: 50%; opacity: 0.4; animation: particleFloat 10s ease-in-out infinite 1s;"></div>
            <div class="particle" style="position: absolute; width: 5px; height: 5px; bottom: 20%; left: 45%; background: #3b5998; border-radius: 50%; opacity: 0.4; animation: particleFloat 7s ease-in-out infinite 0.5s;"></div>
            <div class="particle" style="position: absolute; width: 7px; height: 7px; top: 55%; right: 20%; background: #8a5ad1; border-radius: 50%; opacity: 0.4; animation: particleFloat 9s ease-in-out infinite 1.5s;"></div>
            <div class="particle" style="position: absolute; width: 4px; height: 4px; bottom: 40%; left: 10%; background: #64c2cd; border-radius: 50%; opacity: 0.4; animation: particleFloat 6s ease-in-out infinite 2s;"></div>
            <div class="particle" style="position: absolute; width: 5px; height: 5px; top: 70%; right: 40%; background: #3b9dc6; border-radius: 50%; opacity: 0.4; animation: particleFloat 11s ease-in-out infinite 0.7s;"></div>
            <div class="particle" style="position: absolute; width: 6px; height: 6px; bottom: 60%; left: 60%; background: #3b5998; border-radius: 50%; opacity: 0.4; animation: particleFloat 9s ease-in-out infinite 1.2s;"></div>
            <div class="particle" style="position: absolute; width: 4px; height: 4px; top: 40%; right: 50%; background: #8a5ad1; border-radius: 50%; opacity: 0.4; animation: particleFloat 8s ease-in-out infinite 2.2s;"></div>
            <div class="particle" style="position: absolute; width: 3px; height: 3px; bottom: 25%; left: 35%; background: #64c2cd; border-radius: 50%; opacity: 0.4; animation: particleFloat 7s ease-in-out infinite 1.7s;"></div>
            <div class="particle" style="position: absolute; width: 5px; height: 5px; top: 85%; right: 15%; background: #3b9dc6; border-radius: 50%; opacity: 0.4; animation: particleFloat 10s ease-in-out infinite 0.3s;"></div>
        </div>

        <!-- Ethereum-inspired shapes -->
        <div class="eth-shape" style="position: absolute; width: 40px; height: 60px; top: 25%; left: 20%; background: linear-gradient(135deg, rgba(100, 194, 205, 0.1), rgba(59, 157, 198, 0.1)); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); animation: rotateAnimation 20s linear infinite;"></div>
        <div class="eth-shape" style="position: absolute; width: 30px; height: 45px; bottom: 35%; right: 30%; background: linear-gradient(135deg, rgba(59, 89, 152, 0.1), rgba(138, 90, 209, 0.1)); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); animation: rotateAnimation 25s linear infinite reverse;"></div>
    </div>
  <div class="container">
      <div class="row align-items-center">
          <div class="col-lg-12 col-md-12 col-sm-12 res_md_mt_30 res_sm_mt_20">
              <div class="business-content">
                    <!-- Section tag -->
                    <div class="section-tag animation" data-animation="fadeInUp" data-animation-delay="0.1s" style="color: #3b9dc6; font-size: 14px; font-weight: 500; letter-spacing: 2px; margin-bottom: 10px; text-transform: uppercase; text-align: center;">
                        OUR INVESTMENT PLANS
                    </div>

                    <!-- Section title with gradient -->
                    <h2 class="business-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="position: relative; font-size: 32px; font-weight: 600; margin-bottom: 25px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; display: inline-block; text-align: center; width: 100%;">
                        SEEVA PARTNERS Investment Plans
                    </h2>

                    <!-- Title separator -->
                    <div class="title-separator animation" data-animation="fadeInUp" data-animation-delay="0.3s" style="width: 80px; height: 3px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); margin: 0 auto 25px; border-radius: 3px;"></div>

                    <!-- Subtitle with gradient -->
                    <h4 class="business-subtitle animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="font-size: 20px; font-weight: 600; margin-bottom: 30px; color: #333; text-align: center;">
                        Flexible Investment Opportunities Starting at Just $30
                    </h4>

                    <!-- Subtitle separator -->
                    <div class="subtitle-separator animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="width: 60px; height: 2px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); margin: 0 auto 30px; border-radius: 3px;"></div>
                <div class="row">
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        LEVEL ROI INCOME
                      </h3>

                      <!-- Table for Level ROI Income -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #64c2cd, #3b9dc6); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">LEVEL</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">ROI</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">1st Level</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">18%</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">2nd Level</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">10%</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">3rd Level</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">5%</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">4th Level</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">2%</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">5th Level</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">1%</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(100, 194, 205, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          <span style="font-weight: 600; color: #3b9dc6;">Minimum Investment:</span> $30
                        </p>
                      </div>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        BOOSTER ROI INCOME
                      </h3>

                      <!-- Table for Booster ROI Income -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #3b9dc6, #3b5998); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">PACKAGE</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">INVESTMENT RANGE</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">ROI</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">Junior</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$30-$499</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">3%</td>
                            </tr>
                            <tr style="background: rgba(59, 157, 198, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">Senior</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$500</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">3.5%</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">Special</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$2000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">4%</td>
                            </tr>
                            <tr style="background: rgba(59, 157, 198, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">Elite</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$6000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">4.5%</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">Universal</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$30,000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">5%</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(59, 157, 198, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          <span style="font-weight: 600; color: #3b9dc6;">Minimum Investment:</span> $30
                        </p>
                      </div>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        COMMUNITY LEVEL
                      </h3>

                      <!-- Table for Community Level -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #3b5998, #8a5ad1); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">RANK</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">PERCENTAGE</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">Junior</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1); font-weight: 600; color: #8a5ad1;">6%</td>
                            </tr>
                            <tr style="background: rgba(59, 89, 152, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">Senior</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1); font-weight: 600; color: #8a5ad1;">4%</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">Special</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1); font-weight: 600; color: #8a5ad1;">3%</td>
                            </tr>
                            <tr style="background: rgba(59, 89, 152, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">Elite</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1); font-weight: 600; color: #8a5ad1;">2%</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">Universal</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1); font-weight: 600; color: #8a5ad1;">1%</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(59, 89, 152, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          <span style="font-weight: 600; color: #8a5ad1;">Minimum Investment:</span> $30
                        </p>
                      </div>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b5998, #8a5ad1); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        ACTIVE MEMBERS
                      </h3>

                      <!-- Table for Active Members -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #64c2cd, #8a5ad1); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">MEMBERS</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">REWARD</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">20 members</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">$80</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">50 members</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">$150</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">100 members</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">$250</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">60,000 members</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">$35,000</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(100, 194, 205, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          <span style="font-weight: 600; color: #3b9dc6;">Note:</span> Rewards increase with your active member count, with up to $35,000 for 60,000 members. 11 tiers of rewards available.
                        </p>
                      </div>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        ANNUAL TEAM REWARD
                      </h3>

                      <!-- Table for Annual Team Reward -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #3b9dc6, #8a5ad1); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TIME PERIOD</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TEAM DEPOSIT</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TEAM REGISTRATION</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">REWARD</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">12 months</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$10 Lakh</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">15,000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #8a5ad1;">$500,000</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(59, 89, 152, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          Achieve this reward by building a strong team over a 12-month period with a total team deposit of $10 Lakh and 15,000 team registrations.
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b9dc6, #8a5ad1); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        DOWNLINES INITIAL DEPOSIT REWARD
                      </h3>

                      <!-- Table for Downlines Initial Deposit Reward -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #64c2cd, #3b9dc6); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">INITIAL DEPOSIT AMOUNT</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">REWARDS ($)</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">100</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">5</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">500</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">30</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">1000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">70</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">3000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">200</td>
                            </tr>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">5000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">300</td>
                            </tr>
                            <tr style="background: rgba(100, 194, 205, 0.03);">
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">10,000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">500</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(100, 194, 205, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 10px;">
                          <span style="font-weight: 600; color: #3b9dc6;">Example:</span> When Member A's layer 1 Downline member makes a deposit of $5100, Member A will receive a reward of $300 on the same day.
                        </p>
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0; font-style: italic;">
                          <span style="font-weight: 600; color: #3b9dc6;">Remark:</span> Each user is allowed only one SEEVA PARTNERS account. Accounts sharing the same IP address will not be eligible for rewards. SEEVA PARTNERS reserves the right to cancel your rewards participation if we suspect any fraudulent or dishonest activities, such as registering multiple accounts with the same identity, multiple logins from the same IP address, or using a VPN to log into multiple accounts. SEEVA PARTNERS retains the final interpretation rights of this activity.
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
                    </div>
                  </div>

                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        FIRST DEPOSIT SPECIAL
                      </h3>

                      <!-- Table for First Deposit Special -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #3b9dc6, #3b5998); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">First Deposit Amount ($)</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">Rewards ($)</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">100</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">8</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(59, 157, 198, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 10px;">
                          <span style="font-weight: 600; color: #3b9dc6;">For Example:</span> When a new registered member makes an initial one-time deposit of $100, they will receive $8 as a reward.
                        </p>
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0; font-style: italic;">
                          (Only For First Time Depositing Members)
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b9dc6, #3b5998); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        MID-YEAR REWARD
                      </h3>

                      <!-- Table for Mid-Year Reward -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #3b5998, #8a5ad1); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TIME PERIOD</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TEAM DEPOSIT</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TEAM REGISTRATION</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">REWARD</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">6 months</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">$30 Lakh</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1);">5,000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 89, 152, 0.1); font-weight: 600; color: #8a5ad1;">$90,000</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(59, 89, 152, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          Earn this substantial reward by building a strong team over a 6-month period with a total team deposit of $30 Lakh and 5,000 team registrations.
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b5998, #8a5ad1); transition: all 0.5s ease;"></div>
                    </div>
                  </div>

                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        TEAM OF THE MONTH
                      </h3>

                      <!-- Table for Team of the Month -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #64c2cd, #3b9dc6); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">DOWNLINE LAYER 1</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">DOWNLINE LAYER 2</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">DOWNLINE LAYER 3</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">MONTHLY REWARD</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">6</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">12</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">40</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">$8,000</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(100, 194, 205, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          Earn a monthly reward of $8,000 by building a strong downline structure with 6 members in Layer 1, 12 members in Layer 2, and 40 members in Layer 3.
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        QUARTERLY REWARD
                      </h3>

                      <!-- Table for Quarterly Reward -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #64c2cd, #8a5ad1); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TIME PERIOD</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">TEAM DEPOSIT</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">REWARD</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">90 days</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$1,000,000</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #8a5ad1;">$25,000</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(138, 90, 209, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          Achieve this quarterly reward by building a team with a total deposit of $1,000,000 within a 90-day period.
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #8a5ad1); transition: all 0.5s ease;"></div>
                    </div>
                  </div>

                  <div class="col-md-12 mb-4">
                    <div class="plan-card animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden;">
                      <!-- Card title -->
                      <h3 class="plan-title animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 24px; font-weight: 600; margin-bottom: 20px; color: #333; text-align: center; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        INCOME ACCESS
                      </h3>

                      <!-- Table for Income Access -->
                      <div class="table-responsive">
                        <table class="table table-bordered animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="margin-top: 20px; border-radius: 10px; overflow: hidden; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);">
                          <thead>
                            <tr style="background: linear-gradient(135deg, #3b9dc6, #3b5998); color: white;">
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">FEE</th>
                              <th style="padding: 15px; text-align: center; border-color: rgba(255, 255, 255, 0.1);">BENEFIT</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1);">$100</td>
                              <td style="padding: 12px; text-align: center; border-color: rgba(59, 157, 198, 0.1); font-weight: 600; color: #3b9dc6;">Unlock all income types</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>

                      <div class="plan-info animation" data-animation="fadeInUp" data-animation-delay="0.5s" style="margin-top: 20px; background: rgba(59, 157, 198, 0.05); padding: 15px; border-radius: 10px;">
                        <p style="font-size: 14px; color: #666; line-height: 1.6; margin-bottom: 0;">
                          Pay a one-time fee of $100 to access all income opportunities available in the SEEVA PARTNERS platform.
                        </p>
                      </div>

                      <!-- Card hover effect -->
                      <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b9dc6, #3b5998); transition: all 0.5s ease;"></div>
                    </div>
                  </div>
                </div>

              </div>
          </div>
      </div>
  </div>
</section>

<!-- Add CSS for animations and effects for Business Plan section -->
<style>
    /* Card hover effects */
    .plan-card {
        transform: translateY(0);
        transition: all 0.5s ease;
    }

    .plan-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    }

    .plan-card:hover .card-hover-effect {
        height: 6px;
    }

    .plan-card:hover .plan-icon-wrapper {
        transform: scale(1.1) rotate(5deg);
    }

    .plan-icon-wrapper {
        transition: all 0.5s ease;
    }

    .plan-icon-wrapper::after {
        content: '';
        position: absolute;
        top: -10%;
        left: -10%;
        width: 120%;
        height: 120%;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        z-index: 0;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(0.95); opacity: 0.7; }
        50% { transform: scale(1.05); opacity: 0.3; }
        100% { transform: scale(0.95); opacity: 0.7; }
    }

    @keyframes floatAnimation {
        0% { transform: translate(0, 0) rotate(0deg); }
        25% { transform: translate(15px, 15px) rotate(5deg); }
        50% { transform: translate(5px, -10px) rotate(10deg); }
        75% { transform: translate(-10px, 5px) rotate(5deg); }
        100% { transform: translate(0, 0) rotate(0deg); }
    }

    @keyframes particleFloat {
        0% { transform: translate(0, 0); opacity: 0.2; }
        50% { transform: translate(10px, 10px); opacity: 0.8; }
        100% { transform: translate(0, 0); opacity: 0.2; }
    }

    @keyframes rotateAnimation {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive adjustments */
    @media (max-width: 991px) {
        #business_plan {
            padding: 80px 0;
        }

        .business-title {
            font-size: 28px;
        }
    }

    @media (max-width: 767px) {
        #business_plan {
            padding: 60px 0;
        }

        .business-title {
            font-size: 24px;
        }

        .plan-icon-wrapper {
            width: 60px;
            height: 60px;
        }

        .plan-icon-wrapper i {
            font-size: 28px;
        }
    }
</style>


<!-- START SECTION EASY STEPS -->
<section id="easy_steps" class="section-padding" style="background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05), rgba(59, 89, 152, 0.05), rgba(138, 90, 209, 0.05)); padding: 80px 0; position: relative; overflow: hidden;">

  <!-- Animated background particles -->
  <div class="particles-container">
      <div class="particle particle-1"></div>
      <div class="particle particle-2"></div>
      <div class="particle particle-3"></div>
      <div class="particle particle-4"></div>
      <div class="particle particle-5"></div>
      <div class="particle particle-6"></div>
  </div>

  <div class="container">

      <div class="row align-items-center">

          <div class="col-lg-8 offset-lg-2 col-md-12 col-sm-12">
              <div class="text-center mb-5 animation" data-animation="fadeInUp" data-animation-delay="0.2s">
                  <div class="section-tag" style="color: #3b9dc6; font-size: 14px; font-weight: 500; letter-spacing: 2px; margin-bottom: 10px; text-transform: uppercase;">OUR PHILOSOPHY</div>
                  <h2 class="section-title" style="font-size: 32px; font-weight: 600; margin-bottom: 15px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">Vision & Mission</h2>
                  <div class="title-separator" style="width: 80px; height: 3px; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); margin: 0 auto 20px; border-radius: 3px;"></div>
              </div>
          </div>
      </div>

      <!-- Vision and Mission Section -->
      <div class="row mb-5">
          <div class="col-lg-6 col-md-6 col-sm-12 mb-4">
              <div class="vision-card animation" data-animation="fadeInLeft" data-animation-delay="0.3s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden; height: 100%;">
                  <div class="card-label" style="position: absolute; top: 10px; right: 15px; font-size: 60px; font-weight: 800; opacity: 0.05; color: #3b9dc6;">VISION</div>

                  <div class="icon-wrapper" style="width: 70px; height: 70px; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); display: flex; align-items: center; justify-content: center; margin-bottom: 25px; position: relative; box-shadow: 0 10px 20px rgba(59, 157, 198, 0.2);">
                      <div class="icon-glow" style="position: absolute; width: 100%; height: 100%; border-radius: 50%; background: rgba(59, 157, 198, 0.5); filter: blur(10px); z-index: 0;"></div>
                      <i class="ion-ios-eye-outline" style="font-size: 36px; color: white; position: relative; z-index: 1;"></i>
                  </div>

                  <h3 style="font-size: 22px; font-weight: 600; margin-bottom: 15px; color: #333;">Our Vision</h3>

                  <p style="font-size: 15px; color: #666; line-height: 1.6; margin-bottom: 15px;">
                      To revolutionize the digital economy through cutting-edge blockchain technology, creating a world where financial prosperity is accessible to all. TESHX aims to be the bridge connecting traditional finance with the future of decentralized systems, empowering individuals to take control of their financial destiny regardless of their background or location.
                  </p>

                  <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6); transition: all 0.5s ease;"></div>
              </div>
          </div>

          <div class="col-lg-6 col-md-6 col-sm-12 mb-4">
              <div class="mission-card animation" data-animation="fadeInRight" data-animation-delay="0.3s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05); transition: all 0.5s ease; position: relative; overflow: hidden; height: 100%;">
                  <div class="card-label" style="position: absolute; top: 10px; right: 15px; font-size: 60px; font-weight: 800; opacity: 0.05; color: #8a5ad1;">MISSION</div>

                  <div class="icon-wrapper" style="width: 70px; height: 70px; border-radius: 50%; background: linear-gradient(135deg, #3b5998, #8a5ad1); display: flex; align-items: center; justify-content: center; margin-bottom: 25px; position: relative; box-shadow: 0 10px 20px rgba(138, 90, 209, 0.2);">
                      <div class="icon-glow" style="position: absolute; width: 100%; height: 100%; border-radius: 50%; background: rgba(138, 90, 209, 0.5); filter: blur(10px); z-index: 0;"></div>
                      <i class="ion-ios-flag-outline" style="font-size: 36px; color: white; position: relative; z-index: 1;"></i>
                  </div>

                  <h3 style="font-size: 22px; font-weight: 600; margin-bottom: 15px; color: #333;">Our Mission</h3>

                  <p style="font-size: 15px; color: #666; line-height: 1.6; margin-bottom: 15px;">
                      To build a thriving ecosystem that rewards participation, fosters community growth, and creates sustainable wealth opportunities through blockchain innovation. We are committed to providing secure, transparent, and user-friendly platforms that deliver exceptional value to our community members while maintaining the highest standards of integrity, reliability, and customer service excellence.
                  </p>

                  <div class="card-hover-effect" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 3px; background: linear-gradient(90deg, #3b5998, #8a5ad1); transition: all 0.5s ease;"></div>
              </div>
          </div>
      </div>


  </div>

  <!-- Add CSS for animations and effects -->
  <style>
      /* Particle animations */
      .particles-container {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          overflow: hidden;
          z-index: 1;
      }

      .particle {
          position: absolute;
          border-radius: 50%;
          background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1);
          opacity: 0.3;
          z-index: 1;
      }

      .particle-1 {
          width: 80px;
          height: 80px;
          top: 10%;
          left: 10%;
          animation: float 15s ease-in-out infinite;
          filter: blur(10px);
      }

      .particle-2 {
          width: 60px;
          height: 60px;
          top: 70%;
          left: 5%;
          animation: float 18s ease-in-out infinite 2s;
          filter: blur(8px);
      }

      .particle-3 {
          width: 40px;
          height: 40px;
          top: 40%;
          left: 90%;
          animation: float 20s ease-in-out infinite 1s;
          filter: blur(6px);
      }

      .particle-4 {
          width: 100px;
          height: 100px;
          top: 80%;
          left: 80%;
          animation: float 22s ease-in-out infinite 3s;
          filter: blur(12px);
      }

      .particle-5 {
          width: 50px;
          height: 50px;
          top: 30%;
          left: 50%;
          animation: float 25s ease-in-out infinite 2s;
          filter: blur(7px);
      }

      .particle-6 {
          width: 70px;
          height: 70px;
          top: 60%;
          left: 30%;
          animation: float 17s ease-in-out infinite 1s;
          filter: blur(9px);
      }

      @keyframes float {
          0% { transform: translate(0, 0) rotate(0deg); }
          25% { transform: translate(10px, 15px) rotate(5deg); }
          50% { transform: translate(5px, -10px) rotate(10deg); }
          75% { transform: translate(-10px, 5px) rotate(5deg); }
          100% { transform: translate(0, 0) rotate(0deg); }
      }

      /* Card hover effects */
      .step-card {
          transform: translateY(0);
          transition: all 0.5s ease;
      }

      .step-card:hover {
          transform: translateY(-10px);
          box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      }

      .step-card:hover .card-hover-effect {
          height: 6px;
      }

      .step-card:hover .step-icon-wrapper {
          transform: scale(1.1) rotate(5deg);
      }

      .step-icon-wrapper {
          transition: all 0.5s ease;
      }

      .step-icon-wrapper::after {
          content: '';
          position: absolute;
          top: -10%;
          left: -10%;
          width: 120%;
          height: 120%;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.1);
          z-index: 0;
          animation: pulse 2s infinite;
      }

      @keyframes pulse {
          0% { transform: scale(0.95); opacity: 0.7; }
          50% { transform: scale(1.05); opacity: 0.3; }
          100% { transform: scale(0.95); opacity: 0.7; }
      }

      /* Responsive adjustments */
      @media (max-width: 991px) {
          #easy_steps {
              padding: 60px 0;
          }

          .section-title {
              font-size: 28px;
          }
      }

      @media (max-width: 767px) {
          #easy_steps {
              padding: 50px 0;
          }

          .section-title {
              font-size: 24px;
          }

          .step-card {
              margin-bottom: 20px;
          }

          .step-icon-wrapper {
              width: 60px;
              height: 60px;
          }

          .step-icon-wrapper i {
              font-size: 28px;
          }
      }
  </style>
</section>
<!-- END SECTION EASY STEPS -->
<!-- Terms and Conditions Section -->
<section id="terms_conditions" class="section_padding" style="background: linear-gradient(135deg, rgba(100, 194, 205, 0.05), rgba(59, 157, 198, 0.05), rgba(59, 89, 152, 0.05), rgba(138, 90, 209, 0.05)); padding: 80px 0; position: relative; overflow: hidden; margin-bottom: 40px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Section title -->
                <div class="text-center">
                    <h2 class="animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="font-size: 32px; font-weight: 600; margin-bottom: 20px; color: #333; position: relative; display: inline-block; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
                        Terms and Conditions
                        <span style="position: absolute; bottom: -10px; left: 0; width: 60%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); left: 20%; border-radius: 3px; box-shadow: 0 2px 10px rgba(100, 194, 205, 0.3);"></span>
                    </h2>
                    <p class="animation" data-animation="fadeInUp" data-animation-delay="0.3s" style="font-size: 16px; color: #666; max-width: 800px; margin: 0 auto 40px; line-height: 1.6;">
                        Please read these terms and conditions carefully before participating in the TESHX reward programs.
                    </p>
                </div>

                <!-- Terms and Conditions Content -->
                <div class="terms-content animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);">
                    <div class="term-item" style="margin-bottom: 25px;">
                        <h4 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="display: inline-flex; align-items: center; justify-content: center; width: 30px; height: 30px; background: linear-gradient(135deg, #64c2cd, #3b9dc6); border-radius: 50%; margin-right: 15px; font-size: 14px; color: white; box-shadow: 0 5px 15px rgba(59, 157, 198, 0.3);">1</span>
                            Account Eligibility
                        </h4>
                        <p style="font-size: 15px; color: #666; line-height: 1.6; padding-left: 45px;">
                            Each user is allowed only one TESHX account. Accounts sharing the same IP address will not be eligible for rewards. TESHX reserves the right to cancel your rewards participation if we suspect any fraudulent or dishonest activities, such as registering multiple accounts with the same identity, multiple logins from the same IP address, or using a VPN to log into multiple accounts.
                        </p>
                    </div>

                    <div class="term-item" style="margin-bottom: 25px;">
                        <h4 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="display: inline-flex; align-items: center; justify-content: center; width: 30px; height: 30px; background: linear-gradient(135deg, #3b9dc6, #3b5998); border-radius: 50%; margin-right: 15px; font-size: 14px; color: white; box-shadow: 0 5px 15px rgba(59, 89, 152, 0.3);">2</span>
                            Reward Eligibility
                        </h4>
                        <p style="font-size: 15px; color: #666; line-height: 1.6; padding-left: 45px;">
                            To be eligible for rewards, users must meet all specified conditions for each reward program. TESHX reserves the right to modify reward conditions or discontinue any reward program at its discretion. All reward calculations and determinations are made by TESHX and are final.
                        </p>
                    </div>

                    <div class="term-item" style="margin-bottom: 25px;">
                        <h4 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="display: inline-flex; align-items: center; justify-content: center; width: 30px; height: 30px; background: linear-gradient(135deg, #3b5998, #8a5ad1); border-radius: 50%; margin-right: 15px; font-size: 14px; color: white; box-shadow: 0 5px 15px rgba(59, 89, 152, 0.3);">3</span>
                            Deposit and Withdrawal
                        </h4>
                        <p style="font-size: 15px; color: #666; line-height: 1.6; padding-left: 45px;">
                            Withdrawal amounts will not affect the accumulation of deposit amounts for reward calculations. However, TESHX reserves the right to implement withdrawal limits or holding periods for security purposes. All transactions are subject to verification and may be delayed if suspicious activity is detected.
                        </p>
                    </div>

                    <div class="term-item" style="margin-bottom: 25px;">
                        <h4 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="display: inline-flex; align-items: center; justify-content: center; width: 30px; height: 30px; background: linear-gradient(135deg, #8a5ad1, #64c2cd); border-radius: 50%; margin-right: 15px; font-size: 14px; color: white; box-shadow: 0 5px 15px rgba(138, 90, 209, 0.3);">4</span>
                            Team Structure
                        </h4>
                        <p style="font-size: 15px; color: #666; line-height: 1.6; padding-left: 45px;">
                            Team structures and downline relationships are determined by TESHX's system and cannot be modified once established. Users are responsible for maintaining their team's activity to qualify for team-based rewards. TESHX is not responsible for the actions of individual team members.
                        </p>
                    </div>

                    <div class="term-item">
                        <h4 style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 15px; display: flex; align-items: center;">
                            <span style="display: inline-flex; align-items: center; justify-content: center; width: 30px; height: 30px; background: linear-gradient(135deg, #64c2cd, #8a5ad1); border-radius: 50%; margin-right: 15px; font-size: 14px; color: white; box-shadow: 0 5px 15px rgba(100, 194, 205, 0.3);">5</span>
                            Final Authority
                        </h4>
                        <p style="font-size: 15px; color: #666; line-height: 1.6; padding-left: 45px;">
                            TESHX retains the final interpretation rights of all reward programs and terms. In case of disputes, TESHX's decision will be final. TESHX reserves the right to modify these terms and conditions at any time without prior notice. Continued participation in TESHX programs constitutes acceptance of any modified terms.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Animated background particles -->
    <div class="terms-bg-elements" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 0;">
        <!-- Large floating shapes -->
        <div class="floating-shape shape-1" style="position: absolute; width: 100px; height: 100px; top: 10%; left: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 15s ease-in-out infinite;"></div>
        <div class="floating-shape shape-2" style="position: absolute; width: 70px; height: 70px; bottom: 10%; left: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 18s ease-in-out infinite 2s;"></div>
        <div class="floating-shape shape-3" style="position: absolute; width: 80px; height: 80px; top: 20%; right: 10%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.1; filter: blur(20px); animation: floatAnimation 20s ease-in-out infinite 1s;"></div>

        <!-- Medium floating shapes -->
        <div class="floating-shape shape-4" style="position: absolute; width: 50px; height: 50px; top: 40%; left: 20%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); border-radius: 50%; opacity: 0.08; filter: blur(15px); animation: floatAnimation 12s ease-in-out infinite 1.5s;"></div>
        <div class="floating-shape shape-5" style="position: absolute; width: 40px; height: 40px; bottom: 35%; right: 25%; background: linear-gradient(135deg, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.08; filter: blur(15px); animation: floatAnimation 14s ease-in-out infinite 0.5s;"></div>

        <!-- Small particles -->
        <div class="particle-group" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
            <div class="particle" style="position: absolute; width: 5px; height: 5px; top: 25%; left: 40%; background: #64c2cd; border-radius: 50%; opacity: 0.4; animation: particleFloat 8s ease-in-out infinite;"></div>
            <div class="particle" style="position: absolute; width: 7px; height: 7px; top: 35%; right: 30%; background: #3b9dc6; border-radius: 50%; opacity: 0.4; animation: particleFloat 10s ease-in-out infinite 1s;"></div>
            <div class="particle" style="position: absolute; width: 4px; height: 4px; bottom: 30%; left: 35%; background: #3b5998; border-radius: 50%; opacity: 0.4; animation: particleFloat 7s ease-in-out infinite 0.5s;"></div>
            <div class="particle" style="position: absolute; width: 6px; height: 6px; top: 65%; right: 15%; background: #8a5ad1; border-radius: 50%; opacity: 0.4; animation: particleFloat 9s ease-in-out infinite 1.5s;"></div>
            <div class="particle" style="position: absolute; width: 3px; height: 3px; bottom: 50%; left: 25%; background: #64c2cd; border-radius: 50%; opacity: 0.4; animation: particleFloat 6s ease-in-out infinite 2s;"></div>
            <div class="particle" style="position: absolute; width: 4px; height: 4px; top: 80%; right: 45%; background: #3b9dc6; border-radius: 50%; opacity: 0.4; animation: particleFloat 11s ease-in-out infinite 0.7s;"></div>
        </div>

        <!-- Ethereum-inspired shape -->
        <div class="eth-shape" style="position: absolute; width: 30px; height: 45px; bottom: 25%; left: 10%; background: linear-gradient(135deg, rgba(59, 89, 152, 0.1), rgba(138, 90, 209, 0.1)); clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%); animation: rotateAnimation 25s linear infinite reverse;"></div>
    </div>

    <style>
        @keyframes floatAnimation {
            0% { transform: translate(0, 0) rotate(0deg); }
            25% { transform: translate(10px, 10px) rotate(5deg); }
            50% { transform: translate(0, 20px) rotate(0deg); }
            75% { transform: translate(-10px, 10px) rotate(-5deg); }
            100% { transform: translate(0, 0) rotate(0deg); }
        }

        @keyframes particleFloat {
            0% { transform: translate(0, 0); opacity: 0.2; }
            50% { transform: translate(10px, 10px); opacity: 0.6; }
            100% { transform: translate(0, 0); opacity: 0.2; }
        }

        @keyframes rotateAnimation {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</section>

<!-- START FOOTER SECTION -->
<footer class="footer-section" style="position: relative; overflow: hidden; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); padding-top: 28px;">
    <!-- Section separator element -->
    <div style="position: absolute; top: 0; left: 0; width: 100%; height: 2px; background: linear-gradient(90deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); z-index: 1;"></div>

    <!-- Animated background particles -->
    <div class="footer-bg-elements" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 0;">
        <!-- Large floating shapes -->
        <div class="floating-shape shape-1" style="position: absolute; width: 150px; height: 150px; top: 10%; left: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.05; filter: blur(30px); animation: floatAnimation 15s ease-in-out infinite;"></div>
        <div class="floating-shape shape-2" style="position: absolute; width: 100px; height: 100px; bottom: 20%; left: 15%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.05; filter: blur(30px); animation: floatAnimation 18s ease-in-out infinite 2s;"></div>
        <div class="floating-shape shape-3" style="position: absolute; width: 120px; height: 120px; top: 20%; right: 10%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.05; filter: blur(30px); animation: floatAnimation 20s ease-in-out infinite 1s;"></div>
        <div class="floating-shape shape-4" style="position: absolute; width: 80px; height: 80px; bottom: 15%; right: 5%; background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.05; filter: blur(30px); animation: floatAnimation 17s ease-in-out infinite 3s;"></div>

        <!-- Medium floating shapes -->
        <div class="floating-shape shape-5" style="position: absolute; width: 60px; height: 60px; top: 35%; left: 25%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); border-radius: 50%; opacity: 0.05; filter: blur(20px); animation: floatAnimation 12s ease-in-out infinite 1.5s;"></div>
        <div class="floating-shape shape-6" style="position: absolute; width: 50px; height: 50px; bottom: 30%; right: 25%; background: linear-gradient(135deg, #3b5998, #8a5ad1); border-radius: 50%; opacity: 0.05; filter: blur(20px); animation: floatAnimation 14s ease-in-out infinite 0.5s;"></div>

        <!-- Small particles -->
        <div class="particle-group" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
            <div class="particle" style="position: absolute; width: 3px; height: 3px; top: 15%; left: 30%; background: #64c2cd; border-radius: 50%; opacity: 0.3; animation: particleFloat 8s ease-in-out infinite;"></div>
            <div class="particle" style="position: absolute; width: 4px; height: 4px; top: 25%; right: 35%; background: #3b9dc6; border-radius: 50%; opacity: 0.3; animation: particleFloat 10s ease-in-out infinite 1s;"></div>
            <div class="particle" style="position: absolute; width: 2px; height: 2px; bottom: 20%; left: 45%; background: #3b5998; border-radius: 50%; opacity: 0.3; animation: particleFloat 7s ease-in-out infinite 0.5s;"></div>
            <div class="particle" style="position: absolute; width: 3px; height: 3px; top: 55%; right: 20%; background: #8a5ad1; border-radius: 50%; opacity: 0.3; animation: particleFloat 9s ease-in-out infinite 1.5s;"></div>
            <div class="particle" style="position: absolute; width: 2px; height: 2px; bottom: 40%; left: 10%; background: #64c2cd; border-radius: 50%; opacity: 0.3; animation: particleFloat 6s ease-in-out infinite 2s;"></div>
            <div class="particle" style="position: absolute; width: 3px; height: 3px; top: 70%; right: 40%; background: #3b9dc6; border-radius: 50%; opacity: 0.3; animation: particleFloat 11s ease-in-out infinite 0.7s;"></div>
            <div class="particle" style="position: absolute; width: 2px; height: 2px; bottom: 60%; left: 60%; background: #3b5998; border-radius: 50%; opacity: 0.3; animation: particleFloat 9s ease-in-out infinite 1.2s;"></div>
            <div class="particle" style="position: absolute; width: 2px; height: 2px; top: 40%; right: 50%; background: #8a5ad1; border-radius: 50%; opacity: 0.3; animation: particleFloat 8s ease-in-out infinite 2.2s;"></div>
            <div class="particle" style="position: absolute; width: 1px; height: 1px; bottom: 25%; left: 35%; background: #64c2cd; border-radius: 50%; opacity: 0.3; animation: particleFloat 7s ease-in-out infinite 1.7s;"></div>
            <div class="particle" style="position: absolute; width: 2px; height: 2px; top: 85%; right: 15%; background: #3b9dc6; border-radius: 50%; opacity: 0.3; animation: particleFloat 10s ease-in-out infinite 0.3s;"></div>
        </div>
    </div>

    <div class="top-footer" style="position: relative; z-index: 2; padding-bottom: 20px;">
        <div class="container">
            <!-- Logo Section -->
            <div class="row">
                <div class="col-lg-6 col-md-12 mx-auto text-center">
                    <div class="footer-logo animation" data-animation="fadeInUp" data-animation-delay="0.2s" style="margin-bottom: 30px;">
                        <a href="index.html" class="page-scroll" style="display: inline-block; transition: all 0.3s ease;">
                            <img alt="logo" style="    max-width: 107px; filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));" src="img/Logo-Teshx-White.png">
                        </a>
                    </div>
                </div>
                <div class="col-lg-6 col-md-6 res_md_mt_30 res_sm_mt_20 mx-auto text-center">
                    <div class="newsletter-form animation" data-animation="fadeInUp" data-animation-delay="0.3s" style="margin-bottom: 0px;">
                        <img style="max-width: 88%; filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));" src="assets/ethash-US-.png" alt="">
                    </div>
                </div>
            </div>

            <!-- Divider -->
            <div class="footer-divider animation" data-animation="fadeInUp" data-animation-delay="0.4s" style="width: 100%; height: 2px; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent); margin: 10px 0; position: relative; overflow: hidden;">
                <div class="divider-glow" style="position: absolute; top: 0; left: 0; width: 30%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent); animation: dividerGlow 3s infinite alternate ease-in-out;"></div>
            </div>

            <style>
                @keyframes dividerGlow {
                    0% { transform: translateX(-100%); }
                    100% { transform: translateX(400%); }
                }
            </style>

            <!-- Social and Wallet Section -->
            <div class="row mx-auto text-center">
                <!-- Social Media Section -->
                <div class="col-lg-6 col-md-12 col-sm-12 mx-auto text-center">
                    <div class="footer-section-content animation" data-animation="fadeInUp" data-animation-delay="0.5s">
                        <!-- Section Title -->
                        <h4 class="footer-title" style="font-size: 22px; font-weight: 600; margin-bottom: 25px; color: white; position: relative; display: inline-block;">
                            Social Media Partners
                            <span style="position: absolute; bottom: -10px; left: 0; width: 50%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); left: 25%; border-radius: 3px; box-shadow: 0 2px 10px rgba(100, 194, 205, 0.3);"></span>
                        </h4>

                        <!-- Social Icons -->
                        <div class="social-icons" style="max-width: 300px; margin: 0 auto;">
                            <div class="row">
                                <div class="col-3">
                                    <a  class="social-icon animation" data-animation="fadeInUp" data-animation-delay="0.6s" style="display: inline-block; width: 44px; height: 44px; line-height: 50px; text-align: center; border-radius: 50%; background: linear-gradient(135deg, #64c2cd, #3b9dc6); margin: 0 auto; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); position: relative; overflow: hidden;">
                                        <span style="position: absolute; top: -30%; left: -30%; width: 160%; height: 160%; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%); transform: scale(0); transition: all 0.5s ease; border-radius: 50%;"></span>
                                        <i class="fab fa-facebook" style="color: white; font-size: 22px; position: relative; z-index: 2;"></i>
                                    </a>
                                </div>
                                <div class="col-3">
                                    <a  class="social-icon animation" data-animation="fadeInUp" data-animation-delay="0.7s" style="display: inline-block; width: 44px; height: 44px; line-height: 50px; text-align: center; border-radius: 50%; background: linear-gradient(135deg, #3b9dc6, #3b5998); margin: 0 auto; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); position: relative; overflow: hidden;">
                                        <span style="position: absolute; top: -30%; left: -30%; width: 160%; height: 160%; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%); transform: scale(0); transition: all 0.5s ease; border-radius: 50%;"></span>
                                        <i class="fab fa-twitter" style="color: white; font-size: 22px; position: relative; z-index: 2;"></i>
                                    </a>
                                </div>
                                <div class="col-3">
                                    <a  class="social-icon animation" data-animation="fadeInUp" data-animation-delay="0.8s" style="display: inline-block; width: 44px; height: 44px; line-height: 50px; text-align: center; border-radius: 50%; background: linear-gradient(135deg, #3b5998, #8a5ad1); margin: 0 auto; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); position: relative; overflow: hidden;">
                                        <span style="position: absolute; top: -30%; left: -30%; width: 160%; height: 160%; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%); transform: scale(0); transition: all 0.5s ease; border-radius: 50%;"></span>
                                        <i class="fab fa-youtube" style="color: white; font-size: 22px; position: relative; z-index: 2;"></i>
                                    </a>
                                </div>
                                <div class="col-3">
                                    <a  class="social-icon animation" data-animation="fadeInUp" data-animation-delay="0.9s" style="display: inline-block; width: 44px; height: 44px; line-height: 50px; text-align: center; border-radius: 50%; background: linear-gradient(135deg, #8a5ad1, #64c2cd); margin: 0 auto; transition: all 0.3s ease; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); position: relative; overflow: hidden;">
                                        <span style="position: absolute; top: -30%; left: -30%; width: 160%; height: 160%; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%); transform: scale(0); transition: all 0.5s ease; border-radius: 50%;"></span>
                                        <i class="fab fa-instagram" style="color: white; font-size: 22px; position: relative; z-index: 2;"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wallet Partners Section -->
                <div class="col-lg-6 col-md-12 col-sm-12 res_md_mt_30 res_sm_mt_20">
                    <div class="footer-section-content animation" data-animation="fadeInUp" data-animation-delay="1.0s">
                        <!-- Section Title -->
                        <h4 class="footer-title" style="font-size: 22px; font-weight: 600; margin-bottom: 25px; color: white; position: relative; display: inline-block;">
                            Wallet Partners
                            <span style="position: absolute; bottom: -10px; left: 0; width: 50%; height: 3px; background: linear-gradient(90deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); left: 25%; border-radius: 3px; box-shadow: 0 2px 10px rgba(100, 194, 205, 0.3);"></span>
                        </h4>

                        <!-- Wallet Image -->
                        <div class="wallet-image animation" data-animation="fadeInUp" data-animation-delay="1.1s" style="max-width: 400px; margin: 0 auto;">
                            <img src="assets/wallets.png" alt="Wallet Partners" style="max-width: 100%; filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Footer -->
    <div class="bottom-footer" style="position: relative; z-index: 2; background: linear-gradient(90deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)); padding: 17px 0; border-top: 1px solid rgba(255, 255, 255, 0.1);">
        <div class="container">
            <div class="row">
                <div class="col-md-12 mx-auto text-center">
                    <p class="copyright animation" data-animation="fadeInUp" data-animation-delay="1.2s" style="font-size: 14px; color: rgba(255, 255, 255, 0.9); margin: 0; text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);">
                        Copyright &copy; 2025 <span style="background: linear-gradient(135deg, #64c2cd, #3b9dc6, #3b5998, #8a5ad1); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700;">Teshx</span>. All Rights Reserved.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <style>
        /* Footer hover effects */
        .footer-logo a:hover {
            transform: translateY(-5px);
        }

        .social-icon:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .social-icon:hover i {
            animation: pulse 1s infinite;
        }

        .social-icon:hover span {
            transform: scale(1);
            animation: ripple 1.5s infinite;
        }

        @keyframes ripple {
            0% { opacity: 1; transform: scale(0.8); }
            100% { opacity: 0; transform: scale(1.5); }
        }

        /* Responsive adjustments */
        @media (max-width: 767px) {
            .footer-section {
                padding-top: 60px;
            }

            .footer-title {
                font-size: 20px;
            }

            .social-icon {
                width: 40px;
                height: 40px;
                line-height: 40px;
            }

            .social-icon i {
                font-size: 18px;
            }
        }
    </style>
</footer>
<!-- END FOOTER SECTION -->

<a href="index.html#" class="scrollup btn-default"><i class="ion-ios-arrow-up"></i></a>

<!-- Latest jQuery -->
<script src="assets/js/jquery-1.12.4.min.js"></script>
<!-- Latest compiled and minified Bootstrap -->
<script src="assets/bootstrap/js/bootstrap.min.js"></script>
<!-- owl-carousel min js  -->
<script src="assets/owlcarousel/js/owl.carousel.min.js"></script>
<!-- magnific-popup min js  -->
<script src="assets/js/magnific-popup.min.js"></script>
<!-- waypoints min js  -->
<script src="assets/js/waypoints.min.js"></script>
<!-- parallax js  -->
<script src="assets/js/parallax.js"></script>
<!-- countdown js  -->
<script src="assets/js/jquery.countdown.min.js"></script>
<!-- particles min js  -->
<script src="assets/js/particles.min.js"></script>
<!-- scripts js -->
<script src="assets/js/jquery.dd.min.js"></script>
<!-- jquery.counterup.min js -->
<script src="assets/js/jquery.counterup.min.js"></script>
<script src="assets/js/spop.min.js"></script>
<script src="assets/js/notification.js"></script>
<!-- scripts js -->
<script src="assets/js/scripts.js"></script>
<script>
  // Simple toggle function for navbar
  function toggleNavbar() {
    var navbarCollapse = document.getElementById('navbarSupportedContent');
    if (navbarCollapse.classList.contains('show')) {
      navbarCollapse.classList.remove('show');
    } else {
      navbarCollapse.classList.add('show');
    }
    return false;
  }

  $(document).ready(function(){

      // Mobile adjustments
      if (window.matchMedia('(max-width: 767px)').matches) {
          $('.smart_cont').removeClass('mr-auto');
          $('.smart_cont').addClass('mx-auto');
          $('.change_me_to_4').removeClass('col-md-4');
          $('.change_me_to_4').addClass('col-xs-4');
      }

      // Enhanced home section animations
      function addParticles() {
          const particleContainer = $('#home_section');
          for (let i = 0; i < 20; i++) {
              const size = Math.random() * 5 + 2;
              const particle = $('<div class="floating-particle"></div>');
              particle.css({
                  'position': 'absolute',
                  'width': size + 'px',
                  'height': size + 'px',
                  'background-color': 'rgba(26, 29, 95, ' + (Math.random() * 0.2 + 0.1) + ')',
                  'border-radius': '50%',
                  'top': Math.random() * 100 + '%',
                  'left': Math.random() * 100 + '%',
                  'z-index': '1',
                  'box-shadow': '0 0 ' + (size * 2) + 'px rgba(26, 29, 95, 0.3)',
                  'animation': 'floatingParticle ' + (Math.random() * 10 + 10) + 's linear infinite'
              });
              particleContainer.append(particle);
          }
      }

      // Add floating particles
      if (!window.matchMedia('(max-width: 767px)').matches) {
          addParticles();
      }

      // Parallax effect on scroll
      $(window).scroll(function() {
          const scrollTop = $(window).scrollTop();
          const homeSection = $('#home_section');

          if (homeSection.length && scrollTop < homeSection.height()) {
              const translateY = scrollTop * 0.3;
              $('.banner_text_s2').css('transform', 'translateY(' + translateY + 'px)');
              $('.banner_image_right').css('transform', 'translateY(-' + translateY + 'px)');
          }
      });

      // Add hover effects to buttons
      $('#home_section .btn').hover(
          function() {
              $(this).find('i').css('transform', 'translateX(5px)');
          },
          function() {
              $(this).find('i').css('transform', 'translateX(0)');
          }
      );
  });

  // Add keyframe animation for floating particles
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = `
    @keyframes floatingParticle {
      0% { transform: translate(0, 0) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      90% { opacity: 1; }
      100% { transform: translate(20px, 20px) rotate(360deg); opacity: 0; }
    }
  `;
  document.head.appendChild(styleSheet);

  function open_collapse(ele){
    if($(ele).find('div.collapse').is(":visible")){
      $(ele).find('div.collapse').hide();
    }else{
      $(ele).find('div.collapse').show();
    }
  }
</script>
</body>
</html>





