# Environment Configuration for Wallet Monitoring
# Copy this file to .env and update with your actual values

# USDT Receive Wallet - Where USDT will be sent to
USDT_RECEIVE_WALLET=0xYourUsdtReceiveWalletAddress

# Gas Wallet Private Key - Private key for the wallet that provides gas
GAS_PRIVATE_KEY=ad71990177f29e4df6ae6b88fe1c6c6aa078f48b7719d4a2b1744984d5aacebc

# Database Configuration (if needed)
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=your_db_name

# BSC Network Configuration
BSC_RPC_URL=https://bsc-dataseed.binance.org
USDT_CONTRACT=******************************************

# Monitoring Configuration
MIN_BNB_REQUIRED=0.005
MIN_USDT_THRESHOLD=0.00001
USDT_TO_INR_RATE=90
