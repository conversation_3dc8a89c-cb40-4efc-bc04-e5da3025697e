<?php
$title = "Deposit Crypto History";
include_once 'header.php';
$status_arr = array('Panding', 'Success', 'Failed');

// Pagination variables
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10; // Show 10 records per page
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';

// Base query
$base_query = "SELECT f.*, u.login_id,u.pay_address, u.name FROM deposit_block as f"
        . " LEFT JOIN user as u ON u.uid=f.uid"
        . " WHERE f.uid!=0";

// Add search condition if search value is provided
if (!empty($search)) {
    $search_escaped = $search; // Prevent SQL injection
    $base_query .= " AND (u.login_id LIKE '%$search_escaped%' OR u.name LIKE '%$search_escaped%'
                OR f.txid LIKE '%$search_escaped%' OR u.pay_address LIKE '%$search_escaped%')";
}

// Count total records for pagination
$count_query = "SELECT COUNT(*) as total FROM ($base_query) as count_table";
$count_result = my_query($count_query);
$count_row = mysqli_fetch_assoc($count_result);
$total_records = $count_row['total'];
$total_pages = ceil($total_records / $limit);

// Get paginated data
$query = $base_query . " ORDER BY f.datetime DESC LIMIT $offset, $limit";
$result = my_query($query);
$i = $offset;
?>

<!-- Custom CSS for improved pagination and loading time -->
<style>
    /* Custom pagination styling */
    .custom-pagination {
        display: flex;
        justify-content: center;
        margin-top: 20px;
        padding: 10px;
    }

    .pagination-list {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 8px;
        overflow: hidden;
    }

    .pagination-list li {
        border-right: 1px solid rgba(255, 255, 255, 0.05);
    }

    .pagination-list li:last-child {
        border-right: none;
    }

    .pagination-list a, .pagination-list span {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 12px;
        color: #eaecef;
        text-decoration: none;
        transition: all 0.2s ease;
        min-width: 40px;
    }

    .pagination-list a:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .pagination-list .active a {
        background: rgba(14, 203, 129, 0.2);
        color: #0ecb81;
    }

    /* Search box styling */
    .search-container {
        margin-bottom: 20px;
        display: flex;
        justify-content: flex-end;
    }

    .search-box {
        padding: 8px 12px;
        border-radius: 4px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.2);
        color: #fff;
        width: 250px;
        transition: all 0.3s ease;
    }

    .search-box:focus {
        outline: none;
        border-color: #0ecb81;
        box-shadow: 0 0 0 2px rgba(14, 203, 129, 0.2);
    }

    .search-button {
        padding: 8px 16px;
        background: #0ecb81;
        color: #fff;
        border: none;
        border-radius: 4px;
        margin-left: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .search-button:hover {
        background: #0ba46d;
    }

    /* Table styling */
    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        position: relative;
    }

    .custom-table {
        width: 100%;
        border-collapse: collapse;
    }

    .custom-table th, .custom-table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        white-space: nowrap;
    }

    .custom-table th {
        background-color: rgba(0, 0, 0, 0.2);
        color: #eaecef;
        font-weight: 500;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .custom-table tbody tr {
        transition: background-color 0.2s ease;
        animation: fadeIn 0.3s ease-in-out;
    }

    .custom-table tbody tr:hover {
        background-color: rgba(255, 255, 255, 0.03);
    }

    /* Fade in animation for rows */
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Stats container */
    .stats-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        color: #848e9c;
        font-size: 14px;
    }

    /* Loading indicator */
    .loading-indicator {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.7);
        color: #fff;
        padding: 15px 20px;
        border-radius: 4px;
        z-index: 1000;
        display: none;
    }

    /* No results message */
    .no-results {
        text-align: center;
        padding: 30px;
        color: #848e9c;
        font-style: italic;
    }
</style>
<div class="row">
    <div class="col-sm-12">
        <div class="panel panel-bd">
            <div class="panel-body">
                <!-- Search form -->
                <form method="GET" action="" class="search-container">
                    <input type="text" name="search" class="search-box" placeholder="Search by user, txid or address..." value="<?php echo htmlspecialchars($search); ?>">
                    <button type="submit" class="search-button"><i class="fa fa-search"></i> Search</button>
                </form>

                <!-- Stats container -->
                <div class="stats-container">
                    <div>Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $limit, $total_records); ?> of <?php echo $total_records; ?> entries</div>
                </div>

                <!-- Loading indicator -->
                <div class="loading-indicator">
                    <i class="fa fa-spinner fa-spin"></i> Loading data...
                </div>

                <div class="table-responsive">
                    <table class="custom-table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>User</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Fee</th>
                                <th>Net Amount</th>
                                <th>Coin Value</th>
                                <th>TxId</th>
                                <th>Address</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            if (mysqli_num_rows($result) > 0) {
                                while ($row = mysqli_fetch_object($result)) {
                                    $i++;
                            ?>
                            <tr>
                                <td><?php echo $i; ?></td>
                                <td><?php echo $row->login_id; ?></td>
                                <td><?php echo date("d M, Y h:i A", strtotime($row->datetime)); ?></td>
                                <td><?php echo $row->amount; ?></td>
                                <td><?php echo $row->fee; ?></td>
                                <td><?php echo $row->net_amount; ?></td>
                                <td><?php echo $row->amount_coin; ?></td>
                                <td><?php echo $row->txid; ?></td>
                                <td><?php echo $row->pay_address; ?></td>
                                <td><?php echo $row->type; ?></td>
                                <td><?php echo isset($status_arr[$row->status]) ? $status_arr[$row->status] : ''; ?></td>
                                <td><?php if($row->type == 'USDT.TRC20' && $row->status == 0){?><a href="deposit.php?recid=<?php echo $row->recid;?>" onclick="return confirm('Are you sure you want to approve?');">Approve</a> | <a href="deposit.php?recid=<?php echo $row->recid;?>&cancel=1" onclick="return confirm('Are you sure you want to cancel?');">Cancel</a><?php }?></td>
                            </tr>
                            <?php
                                }
                            } else {
                            ?>
                            <tr>
                                <td colspan="12" class="no-results">No deposits found matching your criteria</td>
                            </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>

                <!-- Custom pagination -->
                <?php if ($total_pages > 1) { ?>
                <div class="custom-pagination">
                    <ul class="pagination-list">
                        <?php if ($page > 1) { ?>
                        <li><a href="?page=1<?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" title="First page"><i class="fa fa-angle-double-left"></i></a></li>
                        <li><a href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" title="Previous page"><i class="fa fa-angle-left"></i></a></li>
                        <?php } ?>

                        <?php
                        // Calculate range of page numbers to display
                        $range = 2; // Display 2 pages before and after current page
                        $start_page = max(1, $page - $range);
                        $end_page = min($total_pages, $page + $range);

                        // Always show first page
                        if ($start_page > 1) {
                            echo '<li><a href="?page=1' . (!empty($search) ? '&search='.urlencode($search) : '') . '">1</a></li>';
                            if ($start_page > 2) {
                                echo '<li><span>...</span></li>';
                            }
                        }

                        // Display page numbers
                        for ($i = $start_page; $i <= $end_page; $i++) {
                            echo '<li class="' . ($i == $page ? 'active' : '') . '"><a href="?page=' . $i . (!empty($search) ? '&search='.urlencode($search) : '') . '">' . $i . '</a></li>';
                        }

                        // Always show last page
                        if ($end_page < $total_pages) {
                            if ($end_page < $total_pages - 1) {
                                echo '<li><span>...</span></li>';
                            }
                            echo '<li><a href="?page=' . $total_pages . (!empty($search) ? '&search='.urlencode($search) : '') . '">' . $total_pages . '</a></li>';
                        }
                        ?>

                        <?php if ($page < $total_pages) { ?>
                        <li><a href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" title="Next page"><i class="fa fa-angle-right"></i></a></li>
                        <li><a href="?page=<?php echo $total_pages; ?><?php echo !empty($search) ? '&search='.urlencode($search) : ''; ?>" title="Last page"><i class="fa fa-angle-double-right"></i></a></li>
                        <?php } ?>
                    </ul>
                </div>
                <?php } ?>
            </div>
        </div>
    </div>
</div>

<!-- Custom script for loading indicator -->
<script>
$(document).ready(function() {
    // Show loading indicator when pagination links are clicked
    $('.pagination-list a').on('click', function() {
        $('.loading-indicator').fadeIn(200);
    });

    // Show loading indicator when search form is submitted
    $('.search-container form').on('submit', function() {
        $('.loading-indicator').fadeIn(200);
    });

    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
});
</script>
<?php include_once 'footer.php'; ?>